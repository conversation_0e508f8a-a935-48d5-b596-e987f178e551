{"ast": null, "code": "import request from '../utils/request';\nexport const userService = {\n  // 获取用户列表\n  async getUsers(params) {\n    return await request.get('/yonghu/page', {\n      params\n    });\n  },\n  // 获取用户详情\n  async getUser(id) {\n    return await request.get(`/yonghu/info/${id}`);\n  },\n  // 创建用户\n  async createUser(data) {\n    await request.post('/yonghu/save', data);\n  },\n  // 更新用户\n  async updateUser(data) {\n    await request.post('/yonghu/update', data);\n  },\n  // 删除用户\n  async deleteUser(id) {\n    await request.post(`/yonghu/delete`, {\n      ids: [id]\n    });\n  },\n  // 批量删除用户\n  async deleteUsers(ids) {\n    await request.post('/yonghu/delete', {\n      ids\n    });\n  }\n};", "map": {"version": 3, "names": ["request", "userService", "getUsers", "params", "get", "getUser", "id", "createUser", "data", "post", "updateUser", "deleteUser", "ids", "deleteUsers"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/userService.ts"], "sourcesContent": ["import request from '../utils/request';\nimport { User, PageParams, PageResponse } from '../types';\n\nexport const userService = {\n  // 获取用户列表\n  async getUsers(params: PageParams): Promise<PageResponse<User>> {\n    return await request.get('/yonghu/page', { params });\n  },\n\n  // 获取用户详情\n  async getUser(id: number): Promise<User> {\n    return await request.get(`/yonghu/info/${id}`);\n  },\n\n  // 创建用户\n  async createUser(data: Omit<User, 'id'>): Promise<void> {\n    await request.post('/yonghu/save', data);\n  },\n\n  // 更新用户\n  async updateUser(data: User): Promise<void> {\n    await request.post('/yonghu/update', data);\n  },\n\n  // 删除用户\n  async deleteUser(id: number): Promise<void> {\n    await request.post(`/yonghu/delete`, { ids: [id] });\n  },\n\n  // 批量删除用户\n  async deleteUsers(ids: number[]): Promise<void> {\n    await request.post('/yonghu/delete', { ids });\n  },\n};\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,kBAAkB;AAGtC,OAAO,MAAMC,WAAW,GAAG;EACzB;EACA,MAAMC,QAAQA,CAACC,MAAkB,EAA+B;IAC9D,OAAO,MAAMH,OAAO,CAACI,GAAG,CAAC,cAAc,EAAE;MAAED;IAAO,CAAC,CAAC;EACtD,CAAC;EAED;EACA,MAAME,OAAOA,CAACC,EAAU,EAAiB;IACvC,OAAO,MAAMN,OAAO,CAACI,GAAG,CAAC,gBAAgBE,EAAE,EAAE,CAAC;EAChD,CAAC;EAED;EACA,MAAMC,UAAUA,CAACC,IAAsB,EAAiB;IACtD,MAAMR,OAAO,CAACS,IAAI,CAAC,cAAc,EAAED,IAAI,CAAC;EAC1C,CAAC;EAED;EACA,MAAME,UAAUA,CAACF,IAAU,EAAiB;IAC1C,MAAMR,OAAO,CAACS,IAAI,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAC5C,CAAC;EAED;EACA,MAAMG,UAAUA,CAACL,EAAU,EAAiB;IAC1C,MAAMN,OAAO,CAACS,IAAI,CAAC,gBAAgB,EAAE;MAAEG,GAAG,EAAE,CAACN,EAAE;IAAE,CAAC,CAAC;EACrD,CAAC;EAED;EACA,MAAMO,WAAWA,CAACD,GAAa,EAAiB;IAC9C,MAAMZ,OAAO,CAACS,IAAI,CAAC,gBAAgB,EAAE;MAAEG;IAAI,CAAC,CAAC;EAC/C;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}