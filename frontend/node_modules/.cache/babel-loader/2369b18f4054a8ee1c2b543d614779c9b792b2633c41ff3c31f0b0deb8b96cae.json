{"ast": null, "code": "import request from '../utils/request';\nexport const bookService = {\n  // 图书分类相关\n  async getCategories(params) {\n    return await request.get('/tushufenlei/page', {\n      params\n    });\n  },\n  async getCategory(id) {\n    return await request.get(`/tushufenlei/info/${id}`);\n  },\n  async createCategory(data) {\n    await request.post('/tushufenlei/save', data);\n  },\n  async updateCategory(data) {\n    await request.post('/tushufenlei/update', data);\n  },\n  async deleteCategory(id) {\n    await request.post('/tushufenlei/delete', {\n      ids: [id]\n    });\n  },\n  // 图书信息相关\n  async getBooks(params) {\n    return await request.get('/tushuxinxi/page', {\n      params\n    });\n  },\n  async getBook(id) {\n    return await request.get(`/tushuxinxi/info/${id}`);\n  },\n  async createBook(data) {\n    await request.post('/tushuxinxi/save', data);\n  },\n  async updateBook(data) {\n    await request.post('/tushuxinxi/update', data);\n  },\n  async deleteBook(id) {\n    await request.post('/tushuxinxi/delete', {\n      ids: [id]\n    });\n  },\n  async deleteBooks(ids) {\n    await request.post('/tushuxinxi/delete', {\n      ids\n    });\n  }\n};", "map": {"version": 3, "names": ["request", "bookService", "getCategories", "params", "get", "getCategory", "id", "createCategory", "data", "post", "updateCategory", "deleteCategory", "ids", "getBooks", "getBook", "createBook", "updateBook", "deleteBook", "deleteBooks"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/bookService.ts"], "sourcesContent": ["import request from '../utils/request';\nimport { Book, BookCategory, PageParams, PageResponse } from '../types';\n\nexport const bookService = {\n  // 图书分类相关\n  async getCategories(params: PageParams): Promise<PageResponse<BookCategory>> {\n    return await request.get('/tushufenlei/page', { params });\n  },\n\n  async getCategory(id: number): Promise<BookCategory> {\n    return await request.get(`/tushufenlei/info/${id}`);\n  },\n\n  async createCategory(data: Omit<BookCategory, 'id'>): Promise<void> {\n    await request.post('/tushufenlei/save', data);\n  },\n\n  async updateCategory(data: BookCategory): Promise<void> {\n    await request.post('/tushufenlei/update', data);\n  },\n\n  async deleteCategory(id: number): Promise<void> {\n    await request.post('/tushufenlei/delete', { ids: [id] });\n  },\n\n  // 图书信息相关\n  async getBooks(params: PageParams): Promise<PageResponse<Book>> {\n    return await request.get('/tushuxinxi/page', { params });\n  },\n\n  async getBook(id: number): Promise<Book> {\n    return await request.get(`/tushuxinxi/info/${id}`);\n  },\n\n  async createBook(data: Omit<Book, 'id'>): Promise<void> {\n    await request.post('/tushuxinxi/save', data);\n  },\n\n  async updateBook(data: Book): Promise<void> {\n    await request.post('/tushuxinxi/update', data);\n  },\n\n  async deleteBook(id: number): Promise<void> {\n    await request.post('/tushuxinxi/delete', { ids: [id] });\n  },\n\n  async deleteBooks(ids: number[]): Promise<void> {\n    await request.post('/tushuxinxi/delete', { ids });\n  },\n};\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,kBAAkB;AAGtC,OAAO,MAAMC,WAAW,GAAG;EACzB;EACA,MAAMC,aAAaA,CAACC,MAAkB,EAAuC;IAC3E,OAAO,MAAMH,OAAO,CAACI,GAAG,CAAC,mBAAmB,EAAE;MAAED;IAAO,CAAC,CAAC;EAC3D,CAAC;EAED,MAAME,WAAWA,CAACC,EAAU,EAAyB;IACnD,OAAO,MAAMN,OAAO,CAACI,GAAG,CAAC,qBAAqBE,EAAE,EAAE,CAAC;EACrD,CAAC;EAED,MAAMC,cAAcA,CAACC,IAA8B,EAAiB;IAClE,MAAMR,OAAO,CAACS,IAAI,CAAC,mBAAmB,EAAED,IAAI,CAAC;EAC/C,CAAC;EAED,MAAME,cAAcA,CAACF,IAAkB,EAAiB;IACtD,MAAMR,OAAO,CAACS,IAAI,CAAC,qBAAqB,EAAED,IAAI,CAAC;EACjD,CAAC;EAED,MAAMG,cAAcA,CAACL,EAAU,EAAiB;IAC9C,MAAMN,OAAO,CAACS,IAAI,CAAC,qBAAqB,EAAE;MAAEG,GAAG,EAAE,CAACN,EAAE;IAAE,CAAC,CAAC;EAC1D,CAAC;EAED;EACA,MAAMO,QAAQA,CAACV,MAAkB,EAA+B;IAC9D,OAAO,MAAMH,OAAO,CAACI,GAAG,CAAC,kBAAkB,EAAE;MAAED;IAAO,CAAC,CAAC;EAC1D,CAAC;EAED,MAAMW,OAAOA,CAACR,EAAU,EAAiB;IACvC,OAAO,MAAMN,OAAO,CAACI,GAAG,CAAC,oBAAoBE,EAAE,EAAE,CAAC;EACpD,CAAC;EAED,MAAMS,UAAUA,CAACP,IAAsB,EAAiB;IACtD,MAAMR,OAAO,CAACS,IAAI,CAAC,kBAAkB,EAAED,IAAI,CAAC;EAC9C,CAAC;EAED,MAAMQ,UAAUA,CAACR,IAAU,EAAiB;IAC1C,MAAMR,OAAO,CAACS,IAAI,CAAC,oBAAoB,EAAED,IAAI,CAAC;EAChD,CAAC;EAED,MAAMS,UAAUA,CAACX,EAAU,EAAiB;IAC1C,MAAMN,OAAO,CAACS,IAAI,CAAC,oBAAoB,EAAE;MAAEG,GAAG,EAAE,CAACN,EAAE;IAAE,CAAC,CAAC;EACzD,CAAC;EAED,MAAMY,WAAWA,CAACN,GAAa,EAAiB;IAC9C,MAAMZ,OAAO,CAACS,IAAI,CAAC,oBAAoB,EAAE;MAAEG;IAAI,CAAC,CAAC;EACnD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}