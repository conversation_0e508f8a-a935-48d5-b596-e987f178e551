{"ast": null, "code": "import request from '../utils/request';\nimport { md5 } from 'js-md5';\nexport const authService = {\n  // 用户登录\n  async login(params) {\n    const {\n      username,\n      password,\n      role\n    } = params;\n\n    // 密码MD5加密\n    const encryptedPassword = md5(password);\n    const response = await request.post('/auth/login', {\n      username,\n      password: encryptedPassword,\n      role\n    });\n    return {\n      token: response.token,\n      user: response.user\n    };\n  },\n  // 用户注册（普通用户）\n  async register(params) {\n    const {\n      username,\n      password,\n      name,\n      phone\n    } = params;\n    const encryptedPassword = md5(password);\n\n    // 调用普通用户注册接口，使用后端字段名\n    await request.post('/yonghu/register', {\n      yonghuming: username,\n      // 用户名\n      mima: encryptedPassword,\n      // 密码\n      xingming: name,\n      // 姓名\n      shouji: phone || '' // 手机号\n    });\n  },\n  // 获取当前用户信息\n  async getCurrentUser() {\n    return await request.get('/user/current');\n  },\n  // 更新用户信息\n  async updateProfile(params) {\n    await request.put('/user/profile', params);\n  },\n  // 修改密码\n  async changePassword(params) {\n    const {\n      oldPassword,\n      newPassword\n    } = params;\n    await request.put('/user/password', {\n      oldPassword: md5(oldPassword),\n      newPassword: md5(newPassword)\n    });\n  },\n  // 退出登录\n  async logout() {\n    await request.post('/auth/logout');\n  }\n};", "map": {"version": 3, "names": ["request", "md5", "authService", "login", "params", "username", "password", "role", "encryptedPassword", "response", "post", "token", "user", "register", "name", "phone", "yonghuming", "mima", "xing<PERSON>", "<PERSON><PERSON><PERSON>", "getCurrentUser", "get", "updateProfile", "put", "changePassword", "oldPassword", "newPassword", "logout"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/authService.ts"], "sourcesContent": ["import request from '../utils/request';\nimport { LoginParams, LoginResponse, User } from '../types';\nimport { md5 } from 'js-md5';\n\nexport const authService = {\n  // 用户登录\n  async login(params: LoginParams): Promise<LoginResponse> {\n    const { username, password, role } = params;\n\n    // 密码MD5加密\n    const encryptedPassword = md5(password);\n\n    const response = await request.post('/auth/login', {\n      username,\n      password: encryptedPassword,\n      role,\n    });\n\n    return {\n      token: response.token,\n      user: response.user,\n    };\n  },\n\n  // 用户注册（普通用户）\n  async register(params: {\n    username: string;\n    password: string;\n    name: string;\n    phone?: string;\n    email?: string;\n    role?: string;\n  }): Promise<void> {\n    const { username, password, name, phone } = params;\n    const encryptedPassword = md5(password);\n\n    // 调用普通用户注册接口，使用后端字段名\n    await request.post('/yonghu/register', {\n      yonghuming: username,  // 用户名\n      mima: encryptedPassword,  // 密码\n      xingming: name,  // 姓名\n      shouji: phone || '',  // 手机号\n    });\n  },\n\n  // 获取当前用户信息\n  async getCurrentUser(): Promise<User> {\n    return await request.get('/user/current');\n  },\n\n  // 更新用户信息\n  async updateProfile(params: Partial<User>): Promise<void> {\n    await request.put('/user/profile', params);\n  },\n\n  // 修改密码\n  async changePassword(params: {\n    oldPassword: string;\n    newPassword: string;\n  }): Promise<void> {\n    const { oldPassword, newPassword } = params;\n    \n    await request.put('/user/password', {\n      oldPassword: md5(oldPassword),\n      newPassword: md5(newPassword),\n    });\n  },\n\n  // 退出登录\n  async logout(): Promise<void> {\n    await request.post('/auth/logout');\n  },\n};\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,kBAAkB;AAEtC,SAASC,GAAG,QAAQ,QAAQ;AAE5B,OAAO,MAAMC,WAAW,GAAG;EACzB;EACA,MAAMC,KAAKA,CAACC,MAAmB,EAA0B;IACvD,MAAM;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC;IAAK,CAAC,GAAGH,MAAM;;IAE3C;IACA,MAAMI,iBAAiB,GAAGP,GAAG,CAACK,QAAQ,CAAC;IAEvC,MAAMG,QAAQ,GAAG,MAAMT,OAAO,CAACU,IAAI,CAAC,aAAa,EAAE;MACjDL,QAAQ;MACRC,QAAQ,EAAEE,iBAAiB;MAC3BD;IACF,CAAC,CAAC;IAEF,OAAO;MACLI,KAAK,EAAEF,QAAQ,CAACE,KAAK;MACrBC,IAAI,EAAEH,QAAQ,CAACG;IACjB,CAAC;EACH,CAAC;EAED;EACA,MAAMC,QAAQA,CAACT,MAOd,EAAiB;IAChB,MAAM;MAAEC,QAAQ;MAAEC,QAAQ;MAAEQ,IAAI;MAAEC;IAAM,CAAC,GAAGX,MAAM;IAClD,MAAMI,iBAAiB,GAAGP,GAAG,CAACK,QAAQ,CAAC;;IAEvC;IACA,MAAMN,OAAO,CAACU,IAAI,CAAC,kBAAkB,EAAE;MACrCM,UAAU,EAAEX,QAAQ;MAAG;MACvBY,IAAI,EAAET,iBAAiB;MAAG;MAC1BU,QAAQ,EAAEJ,IAAI;MAAG;MACjBK,MAAM,EAAEJ,KAAK,IAAI,EAAE,CAAG;IACxB,CAAC,CAAC;EACJ,CAAC;EAED;EACA,MAAMK,cAAcA,CAAA,EAAkB;IACpC,OAAO,MAAMpB,OAAO,CAACqB,GAAG,CAAC,eAAe,CAAC;EAC3C,CAAC;EAED;EACA,MAAMC,aAAaA,CAAClB,MAAqB,EAAiB;IACxD,MAAMJ,OAAO,CAACuB,GAAG,CAAC,eAAe,EAAEnB,MAAM,CAAC;EAC5C,CAAC;EAED;EACA,MAAMoB,cAAcA,CAACpB,MAGpB,EAAiB;IAChB,MAAM;MAAEqB,WAAW;MAAEC;IAAY,CAAC,GAAGtB,MAAM;IAE3C,MAAMJ,OAAO,CAACuB,GAAG,CAAC,gBAAgB,EAAE;MAClCE,WAAW,EAAExB,GAAG,CAACwB,WAAW,CAAC;MAC7BC,WAAW,EAAEzB,GAAG,CAACyB,WAAW;IAC9B,CAAC,CAAC;EACJ,CAAC;EAED;EACA,MAAMC,MAAMA,CAAA,EAAkB;IAC5B,MAAM3B,OAAO,CAACU,IAAI,CAAC,cAAc,CAAC;EACpC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}