import React, { useState } from 'react';
import { Layout, <PERSON>u, <PERSON><PERSON>crumb, Avatar, Dropdown, Space, Button } from 'antd';
import { Routes, Route, useNavigate, useLocation } from 'react-router-dom';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  LogoutOutlined,
  DashboardOutlined,
  BookOutlined,
  TeamOutlined,
  ReadOutlined,
  PayCircleOutlined,
  MessageOutlined,
  HeartOutlined,
  SettingOutlined,
  BellOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../contexts/AuthContext';
import { MenuItem } from '../../types';
import Dashboard from '../../pages/Dashboard';
import UserManagement from '../../pages/UserManagement';
import BookCategoryManagement from '../../pages/BookCategoryManagement';
import BookManagement from '../../pages/BookManagement';
import BorrowManagement from '../../pages/BorrowManagement';
import ReturnManagement from '../../pages/ReturnManagement';
import FineManagement from '../../pages/FineManagement';
import MessageManagement from '../../pages/MessageManagement';
import FavoriteManagement from '../../pages/FavoriteManagement';
import NewsManagement from '../../pages/NewsManagement';
import ConfigManagement from '../../pages/ConfigManagement';

const { Header, Sider, Content } = Layout;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const menuItems: MenuItem[] = [
    {
      key: '/dashboard',
      label: '仪表板',
      icon: <DashboardOutlined />,
      path: '/dashboard',
    },
    {
      key: 'user-management',
      label: '用户管理',
      icon: <TeamOutlined />,
      children: [
        {
          key: '/users',
          label: '用户列表',
          path: '/users',
        },
      ],
    },
    {
      key: 'book-management',
      label: '图书管理',
      icon: <BookOutlined />,
      children: [
        {
          key: '/book-categories',
          label: '图书分类',
          path: '/book-categories',
        },
        {
          key: '/books',
          label: '图书信息',
          path: '/books',
        },
      ],
    },
    {
      key: 'borrow-management',
      label: '借阅管理',
      icon: <ReadOutlined />,
      children: [
        {
          key: '/borrows',
          label: '图书借阅',
          path: '/borrows',
        },
        {
          key: '/returns',
          label: '图书归还',
          path: '/returns',
        },
      ],
    },
    {
      key: '/fines',
      label: '缴纳罚金',
      icon: <PayCircleOutlined />,
      path: '/fines',
    },
    {
      key: '/messages',
      label: '留言板管理',
      icon: <MessageOutlined />,
      path: '/messages',
    },
    {
      key: '/favorites',
      label: '我的收藏',
      icon: <HeartOutlined />,
      path: '/favorites',
    },
    {
      key: 'system-management',
      label: '系统管理',
      icon: <SettingOutlined />,
      children: [
        {
          key: '/news',
          label: '公告信息',
          path: '/news',
        },
        {
          key: '/config',
          label: '轮播图管理',
          path: '/config',
        },
      ],
    },
  ];

  const handleMenuClick = ({ key }: { key: string }) => {
    const menuItem = findMenuItem(menuItems, key);
    if (menuItem?.path) {
      navigate(menuItem.path);
    }
  };

  const findMenuItem = (items: MenuItem[], key: string): MenuItem | null => {
    for (const item of items) {
      if (item.key === key) return item;
      if (item.children) {
        const found = findMenuItem(item.children, key);
        if (found) return found;
      }
    }
    return null;
  };

  const getCurrentMenuItem = (): MenuItem | null => {
    return findMenuItem(menuItems, location.pathname);
  };

  const getBreadcrumbItems = () => {
    const currentItem = getCurrentMenuItem();
    if (!currentItem) return [];

    const items = [{ title: '首页' }];
    
    // 查找父级菜单
    for (const item of menuItems) {
      if (item.children) {
        const found = item.children.find(child => child.key === location.pathname);
        if (found) {
          items.push({ title: item.label });
          items.push({ title: found.label });
          return items;
        }
      } else if (item.key === location.pathname) {
        items.push({ title: item.label });
        return items;
      }
    }

    return items;
  };

  const userMenuItems = [
    {
      key: 'profile',
      label: '个人信息',
      icon: <UserOutlined />,
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      label: '退出登录',
      icon: <LogoutOutlined />,
      onClick: logout,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed}>
        <div className="logo">
          {collapsed ? '图书馆' : '图书馆管理系统'}
        </div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
        />
      </Sider>
      <Layout>
        <Header style={{ padding: 0, background: '#fff' }}>
          <div className="header-content">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: '16px', width: 64, height: 64 }}
            />
            <div className="user-info">
              <Space>
                <BellOutlined style={{ fontSize: '16px' }} />
                <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
                  <Space style={{ cursor: 'pointer' }}>
                    <Avatar icon={<UserOutlined />} />
                    <span>{user?.name || user?.username}</span>
                  </Space>
                </Dropdown>
              </Space>
            </div>
          </div>
        </Header>
        <Content style={{ margin: '0 16px' }}>
          <div className="breadcrumb-container">
            <Breadcrumb items={getBreadcrumbItems()} />
          </div>
          <div className="content-wrapper">
            <Routes>
              <Route path="/dashboard" element={<Dashboard />} />
              <Route path="/users" element={<UserManagement />} />
              <Route path="/book-categories" element={<BookCategoryManagement />} />
              <Route path="/books" element={<BookManagement />} />
              <Route path="/borrows" element={<BorrowManagement />} />
              <Route path="/returns" element={<ReturnManagement />} />
              <Route path="/fines" element={<FineManagement />} />
              <Route path="/messages" element={<MessageManagement />} />
              <Route path="/favorites" element={<FavoriteManagement />} />
              <Route path="/news" element={<NewsManagement />} />
              <Route path="/config" element={<ConfigManagement />} />
            </Routes>
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
