{"ast": null, "code": "import request from '../utils/request';\nimport { md5 } from 'js-md5';\nexport const authService = {\n  // 用户登录\n  async login(params) {\n    const {\n      username,\n      password,\n      role\n    } = params;\n\n    // 密码MD5加密\n    const encryptedPassword = md5(password);\n    const response = await request.post('/api/auth/login', {\n      username,\n      password: encryptedPassword,\n      role\n    });\n    return {\n      token: response.token,\n      user: response.user\n    };\n  },\n  // 用户注册\n  async register(params) {\n    const {\n      username,\n      password,\n      name,\n      phone,\n      email,\n      role\n    } = params;\n    const encryptedPassword = md5(password);\n    await request.post('/api/auth/register', {\n      username,\n      password: encryptedPassword,\n      name,\n      phone,\n      email,\n      role: role || 'user' // 默认为普通用户\n    });\n  },\n  // 获取当前用户信息\n  async getCurrentUser() {\n    return await request.get('/api/users/current');\n  },\n  // 更新用户信息\n  async updateProfile(params) {\n    await request.put('/api/users/update', params);\n  },\n  // 修改密码\n  async changePassword(params) {\n    const {\n      oldPassword,\n      newPassword\n    } = params;\n    await request.put('/api/users/change-password', {\n      oldPassword: md5(oldPassword),\n      newPassword: md5(newPassword)\n    });\n  },\n  // 退出登录\n  async logout() {\n    await request.post('/api/auth/logout');\n  }\n};", "map": {"version": 3, "names": ["request", "md5", "authService", "login", "params", "username", "password", "role", "encryptedPassword", "response", "post", "token", "user", "register", "name", "phone", "email", "getCurrentUser", "get", "updateProfile", "put", "changePassword", "oldPassword", "newPassword", "logout"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/authService.ts"], "sourcesContent": ["import request from '../utils/request';\nimport { LoginParams, LoginResponse, User } from '../types';\nimport { md5 } from 'js-md5';\n\nexport const authService = {\n  // 用户登录\n  async login(params: LoginParams): Promise<LoginResponse> {\n    const { username, password, role } = params;\n\n    // 密码MD5加密\n    const encryptedPassword = md5(password);\n\n    const response = await request.post('/api/auth/login', {\n      username,\n      password: encryptedPassword,\n      role,\n    });\n\n    return {\n      token: response.token,\n      user: response.user,\n    };\n  },\n\n  // 用户注册\n  async register(params: {\n    username: string;\n    password: string;\n    name: string;\n    phone?: string;\n    email?: string;\n    role?: string;\n  }): Promise<void> {\n    const { username, password, name, phone, email, role } = params;\n    const encryptedPassword = md5(password);\n\n    await request.post('/api/auth/register', {\n      username,\n      password: encryptedPassword,\n      name,\n      phone,\n      email,\n      role: role || 'user', // 默认为普通用户\n    });\n  },\n\n  // 获取当前用户信息\n  async getCurrentUser(): Promise<User> {\n    return await request.get('/api/users/current');\n  },\n\n  // 更新用户信息\n  async updateProfile(params: Partial<User>): Promise<void> {\n    await request.put('/api/users/update', params);\n  },\n\n  // 修改密码\n  async changePassword(params: {\n    oldPassword: string;\n    newPassword: string;\n  }): Promise<void> {\n    const { oldPassword, newPassword } = params;\n\n    await request.put('/api/users/change-password', {\n      oldPassword: md5(oldPassword),\n      newPassword: md5(newPassword),\n    });\n  },\n\n  // 退出登录\n  async logout(): Promise<void> {\n    await request.post('/api/auth/logout');\n  },\n};\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,kBAAkB;AAEtC,SAASC,GAAG,QAAQ,QAAQ;AAE5B,OAAO,MAAMC,WAAW,GAAG;EACzB;EACA,MAAMC,KAAKA,CAACC,MAAmB,EAA0B;IACvD,MAAM;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC;IAAK,CAAC,GAAGH,MAAM;;IAE3C;IACA,MAAMI,iBAAiB,GAAGP,GAAG,CAACK,QAAQ,CAAC;IAEvC,MAAMG,QAAQ,GAAG,MAAMT,OAAO,CAACU,IAAI,CAAC,iBAAiB,EAAE;MACrDL,QAAQ;MACRC,QAAQ,EAAEE,iBAAiB;MAC3BD;IACF,CAAC,CAAC;IAEF,OAAO;MACLI,KAAK,EAAEF,QAAQ,CAACE,KAAK;MACrBC,IAAI,EAAEH,QAAQ,CAACG;IACjB,CAAC;EACH,CAAC;EAED;EACA,MAAMC,QAAQA,CAACT,MAOd,EAAiB;IAChB,MAAM;MAAEC,QAAQ;MAAEC,QAAQ;MAAEQ,IAAI;MAAEC,KAAK;MAAEC,KAAK;MAAET;IAAK,CAAC,GAAGH,MAAM;IAC/D,MAAMI,iBAAiB,GAAGP,GAAG,CAACK,QAAQ,CAAC;IAEvC,MAAMN,OAAO,CAACU,IAAI,CAAC,oBAAoB,EAAE;MACvCL,QAAQ;MACRC,QAAQ,EAAEE,iBAAiB;MAC3BM,IAAI;MACJC,KAAK;MACLC,KAAK;MACLT,IAAI,EAAEA,IAAI,IAAI,MAAM,CAAE;IACxB,CAAC,CAAC;EACJ,CAAC;EAED;EACA,MAAMU,cAAcA,CAAA,EAAkB;IACpC,OAAO,MAAMjB,OAAO,CAACkB,GAAG,CAAC,oBAAoB,CAAC;EAChD,CAAC;EAED;EACA,MAAMC,aAAaA,CAACf,MAAqB,EAAiB;IACxD,MAAMJ,OAAO,CAACoB,GAAG,CAAC,mBAAmB,EAAEhB,MAAM,CAAC;EAChD,CAAC;EAED;EACA,MAAMiB,cAAcA,CAACjB,MAGpB,EAAiB;IAChB,MAAM;MAAEkB,WAAW;MAAEC;IAAY,CAAC,GAAGnB,MAAM;IAE3C,MAAMJ,OAAO,CAACoB,GAAG,CAAC,4BAA4B,EAAE;MAC9CE,WAAW,EAAErB,GAAG,CAACqB,WAAW,CAAC;MAC7BC,WAAW,EAAEtB,GAAG,CAACsB,WAAW;IAC9B,CAAC,CAAC;EACJ,CAAC;EAED;EACA,MAAMC,MAAMA,CAAA,EAAkB;IAC5B,MAAMxB,OAAO,CAACU,IAAI,CAAC,kBAAkB,CAAC;EACxC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}