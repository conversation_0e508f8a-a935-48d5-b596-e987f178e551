[{"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/index.tsx": "1", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/App.tsx": "2", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/performance.ts": "3", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Login.tsx": "5", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Auth/ProtectedRoute.tsx": "6", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Common/ErrorBoundary.tsx": "7", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Layout/MainLayout.tsx": "8", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/authService.ts": "9", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/request.ts": "10", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Dashboard.tsx": "11", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/UserManagement.tsx": "12", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookManagement.tsx": "13", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ReturnManagement.tsx": "14", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/NewsManagement.tsx": "15", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FavoriteManagement.tsx": "16", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookCategoryManagement.tsx": "17", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BorrowManagement.tsx": "18", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FineManagement.tsx": "19", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ConfigManagement.tsx": "20", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/MessageManagement.tsx": "21", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/userService.ts": "22", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/bookService.ts": "23", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/borrowService.ts": "24", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/returnService.ts": "25", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/fineService.ts": "26", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/messageService.ts": "27"}, {"size": 657, "mtime": 1750308789615, "results": "28", "hashOfConfig": "29"}, {"size": 1004, "mtime": 1750308663564, "results": "30", "hashOfConfig": "29"}, {"size": 6105, "mtime": 1750308759431, "results": "31", "hashOfConfig": "29"}, {"size": 2134, "mtime": 1750309987604, "results": "32", "hashOfConfig": "29"}, {"size": 2312, "mtime": 1750310006709, "results": "33", "hashOfConfig": "29"}, {"size": 724, "mtime": 1750309965887, "results": "34", "hashOfConfig": "29"}, {"size": 1056, "mtime": 1750308619882, "results": "35", "hashOfConfig": "29"}, {"size": 7267, "mtime": 1750310048541, "results": "36", "hashOfConfig": "29"}, {"size": 1605, "mtime": 1750310508559, "results": "37", "hashOfConfig": "29"}, {"size": 2605, "mtime": 1750310844175, "results": "38", "hashOfConfig": "29"}, {"size": 4068, "mtime": 1750310232255, "results": "39", "hashOfConfig": "29"}, {"size": 6209, "mtime": 1750310109778, "results": "40", "hashOfConfig": "29"}, {"size": 14819, "mtime": 1750310529340, "results": "41", "hashOfConfig": "29"}, {"size": 11896, "mtime": 1750310607468, "results": "42", "hashOfConfig": "29"}, {"size": 321, "mtime": 1750304751954, "results": "43", "hashOfConfig": "29"}, {"size": 329, "mtime": 1750304741855, "results": "44", "hashOfConfig": "29"}, {"size": 5057, "mtime": 1750310251679, "results": "45", "hashOfConfig": "29"}, {"size": 13064, "mtime": 1750310546671, "results": "46", "hashOfConfig": "29"}, {"size": 12428, "mtime": 1750310566442, "results": "47", "hashOfConfig": "29"}, {"size": 319, "mtime": 1750304761971, "results": "48", "hashOfConfig": "29"}, {"size": 10253, "mtime": 1750310586330, "results": "49", "hashOfConfig": "29"}, {"size": 926, "mtime": 1750310069298, "results": "50", "hashOfConfig": "29"}, {"size": 1524, "mtime": 1750310090505, "results": "51", "hashOfConfig": "29"}, {"size": 1496, "mtime": 1750310154341, "results": "52", "hashOfConfig": "29"}, {"size": 1310, "mtime": 1750310172532, "results": "53", "hashOfConfig": "29"}, {"size": 1353, "mtime": 1750310192648, "results": "54", "hashOfConfig": "29"}, {"size": 1123, "mtime": 1750310211438, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tyov95", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/index.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/App.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/performance.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Login.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Auth/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Layout/MainLayout.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/request.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/UserManagement.tsx", ["137"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookManagement.tsx", ["138"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ReturnManagement.tsx", ["139"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/NewsManagement.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FavoriteManagement.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookCategoryManagement.tsx", ["140"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BorrowManagement.tsx", ["141"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FineManagement.tsx", ["142"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ConfigManagement.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/MessageManagement.tsx", ["143"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/userService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/bookService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/borrowService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/returnService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/fineService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/messageService.ts", [], [], {"ruleId": "144", "severity": 1, "message": "145", "line": 34, "column": 6, "nodeType": "146", "endLine": 34, "endColumn": 47, "suggestions": "147"}, {"ruleId": "144", "severity": 1, "message": "148", "line": 56, "column": 6, "nodeType": "146", "endLine": 56, "endColumn": 47, "suggestions": "149"}, {"ruleId": "144", "severity": 1, "message": "150", "line": 54, "column": 6, "nodeType": "146", "endLine": 54, "endColumn": 47, "suggestions": "151"}, {"ruleId": "144", "severity": 1, "message": "152", "line": 31, "column": 6, "nodeType": "146", "endLine": 31, "endColumn": 47, "suggestions": "153"}, {"ruleId": "144", "severity": 1, "message": "154", "line": 57, "column": 6, "nodeType": "146", "endLine": 57, "endColumn": 47, "suggestions": "155"}, {"ruleId": "144", "severity": 1, "message": "156", "line": 63, "column": 6, "nodeType": "146", "endLine": 63, "endColumn": 47, "suggestions": "157"}, {"ruleId": "144", "severity": 1, "message": "158", "line": 51, "column": 6, "nodeType": "146", "endLine": 51, "endColumn": 47, "suggestions": "159"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["160"], "React Hook useEffect has a missing dependency: 'fetchBooks'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["161"], "React Hook useEffect has a missing dependency: 'fetchReturns'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["162"], "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["163"], "React Hook useEffect has a missing dependency: 'fetchBorrows'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["164"], "React Hook useEffect has a missing dependency: 'fetchFines'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["165"], "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["166"], {"desc": "167", "fix": "168"}, {"desc": "169", "fix": "170"}, {"desc": "171", "fix": "172"}, {"desc": "173", "fix": "174"}, {"desc": "175", "fix": "176"}, {"desc": "177", "fix": "178"}, {"desc": "179", "fix": "180"}, "Update the dependencies array to be: [fetchUsers, pagination.pageSize]", {"range": "181", "text": "182"}, "Update the dependencies array to be: [fetchBooks, pagination.pageSize]", {"range": "183", "text": "184"}, "Update the dependencies array to be: [fetchReturns, pagination.pageSize]", {"range": "185", "text": "186"}, "Update the dependencies array to be: [fetchCategories, pagination.pageSize]", {"range": "187", "text": "188"}, "Update the dependencies array to be: [fetchBorrows, pagination.pageSize]", {"range": "189", "text": "190"}, "Update the dependencies array to be: [fetchFines, pagination.pageSize]", {"range": "191", "text": "192"}, "Update the dependencies array to be: [fetchMessages, pagination.pageSize]", {"range": "193", "text": "194"}, [830, 871], "[fetchUsers, pagination.pageSize]", [1325, 1366], "[fetchBooks, pagination.pageSize]", [1307, 1348], "[fetchReturns, pagination.pageSize]", [847, 888], "[fetchCategories, pagination.pageSize]", [1389, 1430], "[fetchBorrows, pagination.pageSize]", [1453, 1494], "[fetchFines, pagination.pageSize]", [1275, 1316], "[fetchMessages, pagination.pageSize]"]