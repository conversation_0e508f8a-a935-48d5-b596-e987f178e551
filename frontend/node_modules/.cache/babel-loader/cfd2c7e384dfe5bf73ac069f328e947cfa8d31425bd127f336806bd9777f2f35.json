{"ast": null, "code": "import * as React from 'react';\nvar AddButton = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    editable = props.editable,\n    locale = props.locale,\n    style = props.style;\n  if (!editable || editable.showAdd === false) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"button\", {\n    ref: ref,\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-add\"),\n    style: style,\n    \"aria-label\": (locale === null || locale === void 0 ? void 0 : locale.addAriaLabel) || 'Add tab',\n    onClick: function onClick(event) {\n      editable.onEdit('add', {\n        event: event\n      });\n    }\n  }, editable.addIcon || '+');\n});\nexport default AddButton;", "map": {"version": 3, "names": ["React", "AddButton", "forwardRef", "props", "ref", "prefixCls", "editable", "locale", "style", "showAdd", "createElement", "type", "className", "concat", "addAriaLabel", "onClick", "event", "onEdit", "addIcon"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/node_modules/rc-tabs/es/TabNavList/AddButton.js"], "sourcesContent": ["import * as React from 'react';\nvar AddButton = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    editable = props.editable,\n    locale = props.locale,\n    style = props.style;\n  if (!editable || editable.showAdd === false) {\n    return null;\n  }\n  return /*#__PURE__*/React.createElement(\"button\", {\n    ref: ref,\n    type: \"button\",\n    className: \"\".concat(prefixCls, \"-nav-add\"),\n    style: style,\n    \"aria-label\": (locale === null || locale === void 0 ? void 0 : locale.addAriaLabel) || 'Add tab',\n    onClick: function onClick(event) {\n      editable.onEdit('add', {\n        event: event\n      });\n    }\n  }, editable.addIcon || '+');\n});\nexport default AddButton;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,IAAIC,SAAS,GAAG,aAAaD,KAAK,CAACE,UAAU,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;EAClE,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BC,QAAQ,GAAGH,KAAK,CAACG,QAAQ;IACzBC,MAAM,GAAGJ,KAAK,CAACI,MAAM;IACrBC,KAAK,GAAGL,KAAK,CAACK,KAAK;EACrB,IAAI,CAACF,QAAQ,IAAIA,QAAQ,CAACG,OAAO,KAAK,KAAK,EAAE;IAC3C,OAAO,IAAI;EACb;EACA,OAAO,aAAaT,KAAK,CAACU,aAAa,CAAC,QAAQ,EAAE;IAChDN,GAAG,EAAEA,GAAG;IACRO,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACR,SAAS,EAAE,UAAU,CAAC;IAC3CG,KAAK,EAAEA,KAAK;IACZ,YAAY,EAAE,CAACD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACO,YAAY,KAAK,SAAS;IAChGC,OAAO,EAAE,SAASA,OAAOA,CAACC,KAAK,EAAE;MAC/BV,QAAQ,CAACW,MAAM,CAAC,KAAK,EAAE;QACrBD,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;EACF,CAAC,EAAEV,QAAQ,CAACY,OAAO,IAAI,GAAG,CAAC;AAC7B,CAAC,CAAC;AACF,eAAejB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}