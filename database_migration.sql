-- 数据库重构脚本：统一用户系统
-- 执行前请备份数据库！
-- 使用数据库 t044

USE `t044`;

-- 1. 创建新的统一用户表结构
DROP TABLE IF EXISTS `users_new`;
CREATE TABLE `users_new` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `name` varchar(50) NOT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别',
  `avatar` varchar(200) DEFAULT NULL COMMENT '头像',
  `id_card` varchar(20) DEFAULT NULL COMMENT '身份证号',
  `role` varchar(20) NOT NULL DEFAULT 'user' COMMENT '角色：admin(管理员), user(普通用户)',
  `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT '状态：active(激活), inactive(禁用)',
  `addtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_role` (`role`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb3 COMMENT='统一用户表';

-- 2. 迁移现有管理员数据
INSERT INTO `users_new` (`username`, `password`, `name`, `role`, `status`, `addtime`)
SELECT 
  `username`, 
  `password`, 
  COALESCE(`username`, '管理员') as `name`,
  'admin' as `role`,
  'active' as `status`,
  COALESCE(`addtime`, NOW()) as `addtime`
FROM `users` 
WHERE `username` IS NOT NULL;

-- 3. 迁移现有普通用户数据
INSERT INTO `users_new` (`username`, `password`, `name`, `phone`, `gender`, `id_card`, `role`, `status`, `addtime`)
SELECT 
  `yonghuming` as `username`, 
  `mima` as `password`, 
  COALESCE(`xingming`, `yonghuming`) as `name`,
  `shouji` as `phone`,
  `xingbie` as `gender`,
  `shenfenzheng` as `id_card`,
  'user' as `role`,
  'active' as `status`,
  COALESCE(`addtime`, NOW()) as `addtime`
FROM `yonghu` 
WHERE `yonghuming` IS NOT NULL;

-- 4. 备份旧表并替换
RENAME TABLE `users` TO `users_backup`;
RENAME TABLE `yonghu` TO `yonghu_backup`;
RENAME TABLE `users_new` TO `users`;

-- 5. 插入默认管理员账户（如果不存在）
INSERT IGNORE INTO `users` (`username`, `password`, `name`, `role`, `status`) 
VALUES ('admin', 'admin', '系统管理员', 'admin', 'active');

-- 6. 插入默认普通用户账户（如果不存在）
INSERT IGNORE INTO `users` (`username`, `password`, `name`, `role`, `status`) 
VALUES ('user', 'user', '测试用户', 'user', 'active');

-- 验证数据迁移
SELECT '管理员数量:' as info, COUNT(*) as count FROM users WHERE role = 'admin'
UNION ALL
SELECT '普通用户数量:' as info, COUNT(*) as count FROM users WHERE role = 'user'
UNION ALL
SELECT '总用户数量:' as info, COUNT(*) as count FROM users;

-- 注意：执行完成后，请检查数据是否正确迁移
-- 如果有问题，可以通过以下命令恢复：
-- DROP TABLE users;
-- RENAME TABLE users_backup TO users;
-- RENAME TABLE yonghu_backup TO yonghu;
