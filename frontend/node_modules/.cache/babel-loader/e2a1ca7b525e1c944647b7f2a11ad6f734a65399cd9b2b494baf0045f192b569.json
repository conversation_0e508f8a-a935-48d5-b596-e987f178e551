{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/contexts/AuthContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useEffect } from 'react';\nimport { authService } from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthContext = /*#__PURE__*/createContext(undefined);\nexport const useAuth = () => {\n  _s();\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const AuthProvider = ({\n  children\n}) => {\n  _s2();\n  const [user, setUser] = useState(null);\n  const [token, setToken] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // 从localStorage恢复登录状态\n    const savedToken = localStorage.getItem('token');\n    const savedUser = localStorage.getItem('user');\n    if (savedToken && savedUser) {\n      setToken(savedToken);\n      setUser(JSON.parse(savedUser));\n    }\n    setLoading(false);\n  }, []);\n  const login = async params => {\n    try {\n      const response = await authService.login(params);\n      const {\n        token: newToken,\n        user: newUser\n      } = response;\n      setToken(newToken);\n      setUser(newUser);\n\n      // 保存到localStorage\n      localStorage.setItem('token', newToken);\n      localStorage.setItem('user', JSON.stringify(newUser));\n    } catch (error) {\n      throw error;\n    }\n  };\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  };\n  const value = {\n    user,\n    token,\n    login,\n    logout,\n    loading,\n    isAuthenticated: !!token && !!user\n  };\n  return /*#__PURE__*/_jsxDEV(AuthContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s2(AuthProvider, \"uAkFQMmIUxfxJcQTEb8tCM/KFt4=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useEffect", "authService", "jsxDEV", "_jsxDEV", "AuthContext", "undefined", "useAuth", "_s", "context", "Error", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s2", "user", "setUser", "token", "setToken", "loading", "setLoading", "savedToken", "localStorage", "getItem", "savedUser", "JSON", "parse", "login", "params", "response", "newToken", "newUser", "setItem", "stringify", "error", "logout", "removeItem", "value", "isAuthenticated", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { User, LoginParams, LoginResponse } from '../types';\nimport { authService } from '../services/authService';\n\ninterface AuthContextType {\n  user: User | null;\n  token: string | null;\n  login: (params: LoginParams) => Promise<void>;\n  logout: () => void;\n  loading: boolean;\n  isAuthenticated: boolean;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\ninterface AuthProviderProps {\n  children: ReactNode;\n}\n\nexport const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [token, setToken] = useState<string | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // 从localStorage恢复登录状态\n    const savedToken = localStorage.getItem('token');\n    const savedUser = localStorage.getItem('user');\n    \n    if (savedToken && savedUser) {\n      setToken(savedToken);\n      setUser(JSON.parse(savedUser));\n    }\n    setLoading(false);\n  }, []);\n\n  const login = async (params: LoginParams): Promise<void> => {\n    try {\n      const response: LoginResponse = await authService.login(params);\n      const { token: newToken, user: newUser } = response;\n      \n      setToken(newToken);\n      setUser(newUser);\n      \n      // 保存到localStorage\n      localStorage.setItem('token', newToken);\n      localStorage.setItem('user', JSON.stringify(newUser));\n    } catch (error) {\n      throw error;\n    }\n  };\n\n  const logout = () => {\n    setUser(null);\n    setToken(null);\n    localStorage.removeItem('token');\n    localStorage.removeItem('user');\n  };\n\n  const value: AuthContextType = {\n    user,\n    token,\n    login,\n    logout,\n    loading,\n    isAuthenticated: !!token && !!user,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,SAAS,QAAmB,OAAO;AAExF,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAWtD,MAAMC,WAAW,gBAAGP,aAAa,CAA8BQ,SAAS,CAAC;AAEzE,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,OAAO,GAAGV,UAAU,CAACM,WAAW,CAAC;EACvC,IAAII,OAAO,KAAKH,SAAS,EAAE;IACzB,MAAM,IAAII,KAAK,CAAC,6CAA6C,CAAC;EAChE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,OAAO;AAYpB,OAAO,MAAMI,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACzE,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGf,QAAQ,CAAc,IAAI,CAAC;EACnD,MAAM,CAACgB,KAAK,EAAEC,QAAQ,CAAC,GAAGjB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMmB,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAChD,MAAMC,SAAS,GAAGF,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;IAE9C,IAAIF,UAAU,IAAIG,SAAS,EAAE;MAC3BN,QAAQ,CAACG,UAAU,CAAC;MACpBL,OAAO,CAACS,IAAI,CAACC,KAAK,CAACF,SAAS,CAAC,CAAC;IAChC;IACAJ,UAAU,CAAC,KAAK,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMO,KAAK,GAAG,MAAOC,MAAmB,IAAoB;IAC1D,IAAI;MACF,MAAMC,QAAuB,GAAG,MAAM1B,WAAW,CAACwB,KAAK,CAACC,MAAM,CAAC;MAC/D,MAAM;QAAEX,KAAK,EAAEa,QAAQ;QAAEf,IAAI,EAAEgB;MAAQ,CAAC,GAAGF,QAAQ;MAEnDX,QAAQ,CAACY,QAAQ,CAAC;MAClBd,OAAO,CAACe,OAAO,CAAC;;MAEhB;MACAT,YAAY,CAACU,OAAO,CAAC,OAAO,EAAEF,QAAQ,CAAC;MACvCR,YAAY,CAACU,OAAO,CAAC,MAAM,EAAEP,IAAI,CAACQ,SAAS,CAACF,OAAO,CAAC,CAAC;IACvD,CAAC,CAAC,OAAOG,KAAK,EAAE;MACd,MAAMA,KAAK;IACb;EACF,CAAC;EAED,MAAMC,MAAM,GAAGA,CAAA,KAAM;IACnBnB,OAAO,CAAC,IAAI,CAAC;IACbE,QAAQ,CAAC,IAAI,CAAC;IACdI,YAAY,CAACc,UAAU,CAAC,OAAO,CAAC;IAChCd,YAAY,CAACc,UAAU,CAAC,MAAM,CAAC;EACjC,CAAC;EAED,MAAMC,KAAsB,GAAG;IAC7BtB,IAAI;IACJE,KAAK;IACLU,KAAK;IACLQ,MAAM;IACNhB,OAAO;IACPmB,eAAe,EAAE,CAAC,CAACrB,KAAK,IAAI,CAAC,CAACF;EAChC,CAAC;EAED,oBACEV,OAAA,CAACC,WAAW,CAACiC,QAAQ;IAACF,KAAK,EAAEA,KAAM;IAAAxB,QAAA,EAChCA;EAAQ;IAAA2B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAE3B,CAAC;AAAC7B,GAAA,CAtDWF,YAAyC;AAAAgC,EAAA,GAAzChC,YAAyC;AAAA,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}