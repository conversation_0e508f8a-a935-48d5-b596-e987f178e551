[{"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/index.tsx": "1", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/App.tsx": "2", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/performance.ts": "3", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Login.tsx": "5", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Auth/ProtectedRoute.tsx": "6", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Common/ErrorBoundary.tsx": "7", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Layout/MainLayout.tsx": "8", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/authService.ts": "9", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/request.ts": "10", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Dashboard.tsx": "11", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/UserManagement.tsx": "12", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookManagement.tsx": "13", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ReturnManagement.tsx": "14", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/NewsManagement.tsx": "15", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FavoriteManagement.tsx": "16", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookCategoryManagement.tsx": "17", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BorrowManagement.tsx": "18", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FineManagement.tsx": "19", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ConfigManagement.tsx": "20", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/MessageManagement.tsx": "21", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/userService.ts": "22", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/bookService.ts": "23"}, {"size": 657, "mtime": 1750308789615, "results": "24", "hashOfConfig": "25"}, {"size": 1004, "mtime": 1750308663564, "results": "26", "hashOfConfig": "25"}, {"size": 6105, "mtime": 1750308759431, "results": "27", "hashOfConfig": "25"}, {"size": 2134, "mtime": 1750309987604, "results": "28", "hashOfConfig": "25"}, {"size": 2312, "mtime": 1750310006709, "results": "29", "hashOfConfig": "25"}, {"size": 724, "mtime": 1750309965887, "results": "30", "hashOfConfig": "25"}, {"size": 1056, "mtime": 1750308619882, "results": "31", "hashOfConfig": "25"}, {"size": 7267, "mtime": 1750310048541, "results": "32", "hashOfConfig": "25"}, {"size": 1606, "mtime": 1750310025718, "results": "33", "hashOfConfig": "25"}, {"size": 2194, "mtime": 1750309911098, "results": "34", "hashOfConfig": "25"}, {"size": 4068, "mtime": 1750310232255, "results": "35", "hashOfConfig": "25"}, {"size": 6209, "mtime": 1750310109778, "results": "36", "hashOfConfig": "25"}, {"size": 14817, "mtime": 1750305742329, "results": "37", "hashOfConfig": "25"}, {"size": 11893, "mtime": 1750308389827, "results": "38", "hashOfConfig": "25"}, {"size": 321, "mtime": 1750304751954, "results": "39", "hashOfConfig": "25"}, {"size": 329, "mtime": 1750304741855, "results": "40", "hashOfConfig": "25"}, {"size": 5057, "mtime": 1750310251679, "results": "41", "hashOfConfig": "25"}, {"size": 13060, "mtime": 1750308333770, "results": "42", "hashOfConfig": "25"}, {"size": 12425, "mtime": 1750308463710, "results": "43", "hashOfConfig": "25"}, {"size": 319, "mtime": 1750304761971, "results": "44", "hashOfConfig": "25"}, {"size": 10251, "mtime": 1750308557713, "results": "45", "hashOfConfig": "25"}, {"size": 926, "mtime": 1750310069298, "results": "46", "hashOfConfig": "25"}, {"size": 1524, "mtime": 1750310090505, "results": "47", "hashOfConfig": "25"}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tyov95", {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/index.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/App.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/performance.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Login.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Auth/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Layout/MainLayout.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/request.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/UserManagement.tsx", ["117"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookManagement.tsx", ["118"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ReturnManagement.tsx", ["119"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/NewsManagement.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FavoriteManagement.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookCategoryManagement.tsx", ["120"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BorrowManagement.tsx", ["121"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FineManagement.tsx", ["122"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ConfigManagement.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/MessageManagement.tsx", ["123"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/userService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/bookService.ts", [], [], {"ruleId": "124", "severity": 1, "message": "125", "line": 34, "column": 6, "nodeType": "126", "endLine": 34, "endColumn": 47, "suggestions": "127"}, {"ruleId": "124", "severity": 1, "message": "128", "line": 56, "column": 6, "nodeType": "126", "endLine": 56, "endColumn": 47, "suggestions": "129"}, {"ruleId": "124", "severity": 1, "message": "130", "line": 54, "column": 6, "nodeType": "126", "endLine": 54, "endColumn": 47, "suggestions": "131"}, {"ruleId": "124", "severity": 1, "message": "132", "line": 31, "column": 6, "nodeType": "126", "endLine": 31, "endColumn": 47, "suggestions": "133"}, {"ruleId": "124", "severity": 1, "message": "134", "line": 57, "column": 6, "nodeType": "126", "endLine": 57, "endColumn": 47, "suggestions": "135"}, {"ruleId": "124", "severity": 1, "message": "136", "line": 63, "column": 6, "nodeType": "126", "endLine": 63, "endColumn": 47, "suggestions": "137"}, {"ruleId": "124", "severity": 1, "message": "138", "line": 51, "column": 6, "nodeType": "126", "endLine": 51, "endColumn": 47, "suggestions": "139"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["140"], "React Hook useEffect has a missing dependency: 'fetchBooks'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["141"], "React Hook useEffect has a missing dependency: 'fetchReturns'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["142"], "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["143"], "React Hook useEffect has a missing dependency: 'fetchBorrows'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["144"], "React Hook useEffect has a missing dependency: 'fetchFines'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["145"], "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["146"], {"desc": "147", "fix": "148"}, {"desc": "149", "fix": "150"}, {"desc": "151", "fix": "152"}, {"desc": "153", "fix": "154"}, {"desc": "155", "fix": "156"}, {"desc": "157", "fix": "158"}, {"desc": "159", "fix": "160"}, "Update the dependencies array to be: [fetchUsers, pagination.pageSize]", {"range": "161", "text": "162"}, "Update the dependencies array to be: [fetchBooks, pagination.pageSize]", {"range": "163", "text": "164"}, "Update the dependencies array to be: [fetchReturns, pagination.pageSize]", {"range": "165", "text": "166"}, "Update the dependencies array to be: [fetchCategories, pagination.pageSize]", {"range": "167", "text": "168"}, "Update the dependencies array to be: [fetchBorrows, pagination.pageSize]", {"range": "169", "text": "170"}, "Update the dependencies array to be: [fetchFines, pagination.pageSize]", {"range": "171", "text": "172"}, "Update the dependencies array to be: [fetchMessages, pagination.pageSize]", {"range": "173", "text": "174"}, [830, 871], "[fetchUsers, pagination.pageSize]", [1323, 1364], "[fetchBooks, pagination.pageSize]", [1304, 1345], "[fetchReturns, pagination.pageSize]", [847, 888], "[fetchCategories, pagination.pageSize]", [1385, 1426], "[fetchBorrows, pagination.pageSize]", [1450, 1491], "[fetchFines, pagination.pageSize]", [1273, 1314], "[fetchMessages, pagination.pageSize]"]