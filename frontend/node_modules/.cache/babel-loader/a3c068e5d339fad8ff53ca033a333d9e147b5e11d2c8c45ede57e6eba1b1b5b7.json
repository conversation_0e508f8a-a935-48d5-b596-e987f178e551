{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookManagement.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Table, Button, Space, Modal, Form, Input, Select, InputNumber, message, Popconfirm, Card, Image, Tag, Tooltip } from 'antd';\nimport { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, SearchOutlined } from '@ant-design/icons';\nimport { bookService } from '@/services/bookService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\nconst BookManagement = () => {\n  _s();\n  const [books, setBooks] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingBook, setEditingBook] = useState(null);\n  const [viewingBook, setViewingBook] = useState(null);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0\n  });\n  const [searchParams, setSearchParams] = useState({\n    name: '',\n    author: '',\n    categoryId: undefined\n  });\n  const [form] = Form.useForm();\n  useEffect(() => {\n    fetchBooks();\n    fetchCategories();\n  }, [pagination.current, pagination.pageSize]);\n  const fetchBooks = async () => {\n    setLoading(true);\n    try {\n      const params = {\n        current: pagination.current,\n        size: pagination.pageSize,\n        ...searchParams\n      };\n      const response = await bookService.getBooks(params);\n      setBooks(response.records);\n      setPagination(prev => ({\n        ...prev,\n        total: response.total\n      }));\n    } catch (error) {\n      message.error('获取图书列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchCategories = async () => {\n    try {\n      const response = await bookService.getCategories({\n        current: 1,\n        size: 100\n      });\n      setCategories(response.records);\n    } catch (error) {\n      message.error('获取图书分类失败');\n    }\n  };\n  const handleAdd = () => {\n    setEditingBook(null);\n    setModalVisible(true);\n    form.resetFields();\n  };\n  const handleEdit = record => {\n    setEditingBook(record);\n    setModalVisible(true);\n    form.setFieldsValue(record);\n  };\n  const handleView = record => {\n    setViewingBook(record);\n    setDetailModalVisible(true);\n  };\n  const handleDelete = async id => {\n    try {\n      await bookService.deleteBook(id);\n      message.success('删除成功');\n      fetchBooks();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n  const handleSubmit = async values => {\n    try {\n      if (editingBook) {\n        await bookService.updateBook({\n          ...editingBook,\n          ...values\n        });\n        message.success('更新成功');\n      } else {\n        await bookService.createBook(values);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchBooks();\n    } catch (error) {\n      message.error(editingBook ? '更新失败' : '创建失败');\n    }\n  };\n  const handleSearch = () => {\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n    fetchBooks();\n  };\n  const handleReset = () => {\n    setSearchParams({\n      name: '',\n      author: '',\n      categoryId: undefined\n    });\n    setPagination(prev => ({\n      ...prev,\n      current: 1\n    }));\n    setTimeout(fetchBooks, 100);\n  };\n  const getStatusColor = status => {\n    switch (status) {\n      case '可借阅':\n        return 'green';\n      case '已借出':\n        return 'orange';\n      case '维护中':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '封面',\n    dataIndex: 'cover',\n    key: 'cover',\n    width: 80,\n    render: cover => cover ? /*#__PURE__*/_jsxDEV(Image, {\n      width: 50,\n      height: 60,\n      src: cover,\n      fallback: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 173,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: 50,\n        height: 60,\n        background: '#f0f0f0',\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: \"\\u65E0\\u56FE\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 180,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: '图书名称',\n    dataIndex: 'name',\n    key: 'name',\n    ellipsis: true\n  }, {\n    title: '作者',\n    dataIndex: 'author',\n    key: 'author',\n    ellipsis: true\n  }, {\n    title: 'ISBN',\n    dataIndex: 'isbn',\n    key: 'isbn',\n    width: 120\n  }, {\n    title: '分类',\n    dataIndex: 'categoryName',\n    key: 'categoryName',\n    width: 100\n  }, {\n    title: '价格',\n    dataIndex: 'price',\n    key: 'price',\n    width: 80,\n    render: price => `¥${price}`\n  }, {\n    title: '库存',\n    dataIndex: 'stock',\n    key: 'stock',\n    width: 80\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: getStatusColor(status),\n      children: status\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 229,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleView(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"\\u7F16\\u8F91\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 21\n          }, this),\n          onClick: () => handleEdit(record)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u672C\\u56FE\\u4E66\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"\\u5220\\u9664\",\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"link\",\n            size: \"small\",\n            danger: true,\n            icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 265,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 261,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 237,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 16,\n        padding: 16,\n        background: '#fafafa',\n        borderRadius: 6\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        wrap: true,\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u56FE\\u4E66\\u540D\\u79F0\",\n          value: searchParams.name,\n          onChange: e => setSearchParams(prev => ({\n            ...prev,\n            name: e.target.value\n          })),\n          style: {\n            width: 200\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"\\u4F5C\\u8005\",\n          value: searchParams.author,\n          onChange: e => setSearchParams(prev => ({\n            ...prev,\n            author: e.target.value\n          })),\n          style: {\n            width: 200\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Select, {\n          placeholder: \"\\u9009\\u62E9\\u5206\\u7C7B\",\n          value: searchParams.categoryId,\n          onChange: value => setSearchParams(prev => ({\n            ...prev,\n            categoryId: value\n          })),\n          style: {\n            width: 200\n          },\n          allowClear: true,\n          children: categories.map(category => /*#__PURE__*/_jsxDEV(Option, {\n            value: category.id,\n            children: category.name\n          }, category.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 40\n          }, this),\n          onClick: handleSearch,\n          children: \"\\u641C\\u7D22\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 304,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          onClick: handleReset,\n          children: \"\\u91CD\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 278,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 277,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 17\n        }, this),\n        onClick: handleAdd,\n        children: \"\\u65B0\\u589E\\u56FE\\u4E66\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 313,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Table, {\n      columns: columns,\n      dataSource: books,\n      loading: loading,\n      pagination: {\n        ...pagination,\n        showSizeChanger: true,\n        showQuickJumper: true,\n        showTotal: total => `共 ${total} 条记录`,\n        onChange: (page, pageSize) => {\n          setPagination(prev => ({\n            ...prev,\n            current: page,\n            pageSize: pageSize || 10\n          }));\n        }\n      },\n      rowKey: \"id\",\n      scroll: {\n        x: 1200\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingBook ? '编辑图书' : '新增图书',\n      open: modalVisible,\n      onCancel: () => setModalVisible(false),\n      onOk: () => form.submit(),\n      width: 800,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSubmit,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"name\",\n          label: \"\\u56FE\\u4E66\\u540D\\u79F0\",\n          rules: [{\n            required: true,\n            message: '请输入图书名称'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u56FE\\u4E66\\u540D\\u79F0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 363,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 358,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"author\",\n          label: \"\\u4F5C\\u8005\",\n          rules: [{\n            required: true,\n            message: '请输入作者'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u4F5C\\u8005\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 371,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 366,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"publisher\",\n          label: \"\\u51FA\\u7248\\u793E\",\n          rules: [{\n            required: true,\n            message: '请输入出版社'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u51FA\\u7248\\u793E\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 379,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"isbn\",\n          label: \"ISBN\",\n          rules: [{\n            required: true,\n            message: '请输入ISBN'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165ISBN\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"categoryId\",\n          label: \"\\u56FE\\u4E66\\u5206\\u7C7B\",\n          rules: [{\n            required: true,\n            message: '请选择图书分类'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u56FE\\u4E66\\u5206\\u7C7B\",\n            children: categories.map(category => /*#__PURE__*/_jsxDEV(Option, {\n              value: category.id,\n              children: category.name\n            }, category.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 397,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"price\",\n          label: \"\\u4EF7\\u683C\",\n          rules: [{\n            required: true,\n            message: '请输入价格'\n          }],\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EF7\\u683C\",\n            min: 0,\n            precision: 2,\n            style: {\n              width: '100%'\n            },\n            addonBefore: \"\\xA5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"stock\",\n          label: \"\\u5E93\\u5B58\\u6570\\u91CF\",\n          rules: [{\n            required: true,\n            message: '请输入库存数量'\n          }],\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5E93\\u5B58\\u6570\\u91CF\",\n            min: 0,\n            style: {\n              width: '100%'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 418,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u72B6\\u6001\",\n          rules: [{\n            required: true,\n            message: '请选择状态'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u53EF\\u501F\\u9605\",\n              children: \"\\u53EF\\u501F\\u9605\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u5DF2\\u501F\\u51FA\",\n              children: \"\\u5DF2\\u501F\\u51FA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u7EF4\\u62A4\\u4E2D\",\n              children: \"\\u7EF4\\u62A4\\u4E2D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u56FE\\u4E66\\u63CF\\u8FF0\",\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u56FE\\u4E66\\u63CF\\u8FF0\",\n            rows: 4\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"cover\",\n          label: \"\\u5C01\\u9762\\u56FE\\u7247\",\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5C01\\u9762\\u56FE\\u7247URL\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 453,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 449,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 353,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u56FE\\u4E66\\u8BE6\\u60C5\",\n      open: detailModalVisible,\n      onCancel: () => setDetailModalVisible(false),\n      footer: null,\n      width: 600,\n      children: viewingBook && /*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            gap: 16,\n            marginBottom: 16\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: viewingBook.cover ? /*#__PURE__*/_jsxDEV(Image, {\n              width: 120,\n              height: 150,\n              src: viewingBook.cover,\n              fallback: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                width: 120,\n                height: 150,\n                background: '#f0f0f0',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center'\n              },\n              children: \"\\u65E0\\u5C01\\u9762\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 469,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              children: viewingBook.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 484,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u4F5C\\u8005\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 20\n              }, this), viewingBook.author]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u51FA\\u7248\\u793E\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 486,\n                columnNumber: 20\n              }, this), viewingBook.publisher]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"ISBN\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 20\n              }, this), viewingBook.isbn]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 487,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u5206\\u7C7B\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 488,\n                columnNumber: 20\n              }, this), viewingBook.categoryName]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 488,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u4EF7\\u683C\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 489,\n                columnNumber: 20\n              }, this), \"\\xA5\", viewingBook.price]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u5E93\\u5B58\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 490,\n                columnNumber: 20\n              }, this), viewingBook.stock]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                children: \"\\u72B6\\u6001\\uFF1A\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 20\n              }, this), /*#__PURE__*/_jsxDEV(Tag, {\n                color: getStatusColor(viewingBook.status),\n                children: viewingBook.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 40\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 483,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 13\n        }, this), viewingBook.description && /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            children: \"\\u56FE\\u4E66\\u63CF\\u8FF0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: viewingBook.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 497,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 467,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 459,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 275,\n    columnNumber: 5\n  }, this);\n};\n_s(BookManagement, \"onZrlc1XGDBPM9fuV2UTPJR+wpA=\", false, function () {\n  return [Form.useForm];\n});\n_c = BookManagement;\nexport default BookManagement;\nvar _c;\n$RefreshReg$(_c, \"BookManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Table", "<PERSON><PERSON>", "Space", "Modal", "Form", "Input", "Select", "InputNumber", "message", "Popconfirm", "Card", "Image", "Tag", "<PERSON><PERSON><PERSON>", "PlusOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "SearchOutlined", "bookService", "jsxDEV", "_jsxDEV", "Option", "TextArea", "BookManagement", "_s", "books", "setBooks", "categories", "setCategories", "loading", "setLoading", "modalVisible", "setModalVisible", "detailModalVisible", "setDetailModalVisible", "editingBook", "setEditingBook", "viewingBook", "setViewingBook", "pagination", "setPagination", "current", "pageSize", "total", "searchParams", "setSearchParams", "name", "author", "categoryId", "undefined", "form", "useForm", "fetchBooks", "fetchCategories", "params", "size", "response", "getBooks", "records", "prev", "error", "getCategories", "handleAdd", "resetFields", "handleEdit", "record", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleView", "handleDelete", "id", "deleteBook", "success", "handleSubmit", "values", "updateBook", "createBook", "handleSearch", "handleReset", "setTimeout", "getStatusColor", "status", "columns", "title", "dataIndex", "key", "width", "render", "cover", "height", "src", "fallback", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "background", "display", "alignItems", "justifyContent", "children", "ellipsis", "price", "color", "_", "type", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "marginBottom", "padding", "borderRadius", "wrap", "placeholder", "value", "onChange", "e", "target", "allowClear", "map", "category", "dataSource", "showSizeChanger", "showQuickJumper", "showTotal", "page", "<PERSON><PERSON><PERSON>", "scroll", "x", "open", "onCancel", "onOk", "submit", "destroyOnClose", "layout", "onFinish", "<PERSON><PERSON>", "label", "rules", "required", "min", "precision", "addonBefore", "rows", "footer", "gap", "flex", "publisher", "isbn", "categoryName", "stock", "description", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookManagement.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Table,\n  Button,\n  Space,\n  Modal,\n  Form,\n  Input,\n  Select,\n  InputNumber,\n\n  message,\n  Popconfirm,\n  Card,\n  Image,\n  Tag,\n  Tooltip,\n} from 'antd';\nimport {\n  PlusOutlined,\n  EditOutlined,\n  DeleteOutlined,\n\n  EyeOutlined,\n  SearchOutlined,\n} from '@ant-design/icons';\nimport { Book, BookCategory, PageParams } from '@/types';\nimport { bookService } from '@/services/bookService';\n\nconst { Option } = Select;\nconst { TextArea } = Input;\n\nconst BookManagement: React.FC = () => {\n  const [books, setBooks] = useState<Book[]>([]);\n  const [categories, setCategories] = useState<BookCategory[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [modalVisible, setModalVisible] = useState(false);\n  const [detailModalVisible, setDetailModalVisible] = useState(false);\n  const [editingBook, setEditingBook] = useState<Book | null>(null);\n  const [viewingBook, setViewingBook] = useState<Book | null>(null);\n  const [pagination, setPagination] = useState({\n    current: 1,\n    pageSize: 10,\n    total: 0,\n  });\n  const [searchParams, setSearchParams] = useState({\n    name: '',\n    author: '',\n    categoryId: undefined as number | undefined,\n  });\n  const [form] = Form.useForm();\n\n  useEffect(() => {\n    fetchBooks();\n    fetchCategories();\n  }, [pagination.current, pagination.pageSize]);\n\n  const fetchBooks = async () => {\n    setLoading(true);\n    try {\n      const params: PageParams = {\n        current: pagination.current,\n        size: pagination.pageSize,\n        ...searchParams,\n      };\n      const response = await bookService.getBooks(params);\n      setBooks(response.records);\n      setPagination(prev => ({\n        ...prev,\n        total: response.total,\n      }));\n    } catch (error) {\n      message.error('获取图书列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchCategories = async () => {\n    try {\n      const response = await bookService.getCategories({ current: 1, size: 100 });\n      setCategories(response.records);\n    } catch (error) {\n      message.error('获取图书分类失败');\n    }\n  };\n\n  const handleAdd = () => {\n    setEditingBook(null);\n    setModalVisible(true);\n    form.resetFields();\n  };\n\n  const handleEdit = (record: Book) => {\n    setEditingBook(record);\n    setModalVisible(true);\n    form.setFieldsValue(record);\n  };\n\n  const handleView = (record: Book) => {\n    setViewingBook(record);\n    setDetailModalVisible(true);\n  };\n\n  const handleDelete = async (id: number) => {\n    try {\n      await bookService.deleteBook(id);\n      message.success('删除成功');\n      fetchBooks();\n    } catch (error) {\n      message.error('删除失败');\n    }\n  };\n\n  const handleSubmit = async (values: any) => {\n    try {\n      if (editingBook) {\n        await bookService.updateBook({ ...editingBook, ...values });\n        message.success('更新成功');\n      } else {\n        await bookService.createBook(values);\n        message.success('创建成功');\n      }\n      setModalVisible(false);\n      fetchBooks();\n    } catch (error) {\n      message.error(editingBook ? '更新失败' : '创建失败');\n    }\n  };\n\n  const handleSearch = () => {\n    setPagination(prev => ({ ...prev, current: 1 }));\n    fetchBooks();\n  };\n\n  const handleReset = () => {\n    setSearchParams({\n      name: '',\n      author: '',\n      categoryId: undefined,\n    });\n    setPagination(prev => ({ ...prev, current: 1 }));\n    setTimeout(fetchBooks, 100);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case '可借阅':\n        return 'green';\n      case '已借出':\n        return 'orange';\n      case '维护中':\n        return 'red';\n      default:\n        return 'default';\n    }\n  };\n\n  const columns = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '封面',\n      dataIndex: 'cover',\n      key: 'cover',\n      width: 80,\n      render: (cover: string) => (\n        cover ? (\n          <Image\n            width={50}\n            height={60}\n            src={cover}\n            fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n          />\n        ) : (\n          <div style={{ width: 50, height: 60, background: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n            无图\n          </div>\n        )\n      ),\n    },\n    {\n      title: '图书名称',\n      dataIndex: 'name',\n      key: 'name',\n      ellipsis: true,\n    },\n    {\n      title: '作者',\n      dataIndex: 'author',\n      key: 'author',\n      ellipsis: true,\n    },\n    {\n      title: 'ISBN',\n      dataIndex: 'isbn',\n      key: 'isbn',\n      width: 120,\n    },\n    {\n      title: '分类',\n      dataIndex: 'categoryName',\n      key: 'categoryName',\n      width: 100,\n    },\n    {\n      title: '价格',\n      dataIndex: 'price',\n      key: 'price',\n      width: 80,\n      render: (price: number) => `¥${price}`,\n    },\n    {\n      title: '库存',\n      dataIndex: 'stock',\n      key: 'stock',\n      width: 80,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: string) => (\n        <Tag color={getStatusColor(status)}>{status}</Tag>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      render: (_: any, record: Book) => (\n        <Space size=\"small\">\n          <Tooltip title=\"查看详情\">\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<EyeOutlined />}\n              onClick={() => handleView(record)}\n            />\n          </Tooltip>\n          <Tooltip title=\"编辑\">\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<EditOutlined />}\n              onClick={() => handleEdit(record)}\n            />\n          </Tooltip>\n          <Popconfirm\n            title=\"确定要删除这本图书吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Tooltip title=\"删除\">\n              <Button\n                type=\"link\"\n                size=\"small\"\n                danger\n                icon={<DeleteOutlined />}\n              />\n            </Tooltip>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <Card>\n      {/* 搜索区域 */}\n      <div style={{ marginBottom: 16, padding: 16, background: '#fafafa', borderRadius: 6 }}>\n        <Space wrap>\n          <Input\n            placeholder=\"图书名称\"\n            value={searchParams.name}\n            onChange={(e) => setSearchParams(prev => ({ ...prev, name: e.target.value }))}\n            style={{ width: 200 }}\n          />\n          <Input\n            placeholder=\"作者\"\n            value={searchParams.author}\n            onChange={(e) => setSearchParams(prev => ({ ...prev, author: e.target.value }))}\n            style={{ width: 200 }}\n          />\n          <Select\n            placeholder=\"选择分类\"\n            value={searchParams.categoryId}\n            onChange={(value) => setSearchParams(prev => ({ ...prev, categoryId: value }))}\n            style={{ width: 200 }}\n            allowClear\n          >\n            {categories.map(category => (\n              <Option key={category.id} value={category.id}>\n                {category.name}\n              </Option>\n            ))}\n          </Select>\n          <Button type=\"primary\" icon={<SearchOutlined />} onClick={handleSearch}>\n            搜索\n          </Button>\n          <Button onClick={handleReset}>重置</Button>\n        </Space>\n      </div>\n\n      {/* 操作按钮 */}\n      <div style={{ marginBottom: 16 }}>\n        <Button\n          type=\"primary\"\n          icon={<PlusOutlined />}\n          onClick={handleAdd}\n        >\n          新增图书\n        </Button>\n      </div>\n\n      {/* 表格 */}\n      <Table\n        columns={columns}\n        dataSource={books}\n        loading={loading}\n        pagination={{\n          ...pagination,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total) => `共 ${total} 条记录`,\n          onChange: (page, pageSize) => {\n            setPagination(prev => ({\n              ...prev,\n              current: page,\n              pageSize: pageSize || 10,\n            }));\n          },\n        }}\n        rowKey=\"id\"\n        scroll={{ x: 1200 }}\n      />\n\n      {/* 编辑/新增模态框 */}\n      <Modal\n        title={editingBook ? '编辑图书' : '新增图书'}\n        open={modalVisible}\n        onCancel={() => setModalVisible(false)}\n        onOk={() => form.submit()}\n        width={800}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSubmit}\n        >\n          <Form.Item\n            name=\"name\"\n            label=\"图书名称\"\n            rules={[{ required: true, message: '请输入图书名称' }]}\n          >\n            <Input placeholder=\"请输入图书名称\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"author\"\n            label=\"作者\"\n            rules={[{ required: true, message: '请输入作者' }]}\n          >\n            <Input placeholder=\"请输入作者\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"publisher\"\n            label=\"出版社\"\n            rules={[{ required: true, message: '请输入出版社' }]}\n          >\n            <Input placeholder=\"请输入出版社\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"isbn\"\n            label=\"ISBN\"\n            rules={[{ required: true, message: '请输入ISBN' }]}\n          >\n            <Input placeholder=\"请输入ISBN\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"categoryId\"\n            label=\"图书分类\"\n            rules={[{ required: true, message: '请选择图书分类' }]}\n          >\n            <Select placeholder=\"请选择图书分类\">\n              {categories.map(category => (\n                <Option key={category.id} value={category.id}>\n                  {category.name}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"price\"\n            label=\"价格\"\n            rules={[{ required: true, message: '请输入价格' }]}\n          >\n            <InputNumber\n              placeholder=\"请输入价格\"\n              min={0}\n              precision={2}\n              style={{ width: '100%' }}\n              addonBefore=\"¥\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"stock\"\n            label=\"库存数量\"\n            rules={[{ required: true, message: '请输入库存数量' }]}\n          >\n            <InputNumber\n              placeholder=\"请输入库存数量\"\n              min={0}\n              style={{ width: '100%' }}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"status\"\n            label=\"状态\"\n            rules={[{ required: true, message: '请选择状态' }]}\n          >\n            <Select placeholder=\"请选择状态\">\n              <Option value=\"可借阅\">可借阅</Option>\n              <Option value=\"已借出\">已借出</Option>\n              <Option value=\"维护中\">维护中</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"图书描述\"\n          >\n            <TextArea placeholder=\"请输入图书描述\" rows={4} />\n          </Form.Item>\n\n          <Form.Item\n            name=\"cover\"\n            label=\"封面图片\"\n          >\n            <Input placeholder=\"请输入封面图片URL\" />\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 详情模态框 */}\n      <Modal\n        title=\"图书详情\"\n        open={detailModalVisible}\n        onCancel={() => setDetailModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        {viewingBook && (\n          <div>\n            <div style={{ display: 'flex', gap: 16, marginBottom: 16 }}>\n              <div>\n                {viewingBook.cover ? (\n                  <Image\n                    width={120}\n                    height={150}\n                    src={viewingBook.cover}\n                    fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n                  />\n                ) : (\n                  <div style={{ width: 120, height: 150, background: '#f0f0f0', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n                    无封面\n                  </div>\n                )}\n              </div>\n              <div style={{ flex: 1 }}>\n                <h3>{viewingBook.name}</h3>\n                <p><strong>作者：</strong>{viewingBook.author}</p>\n                <p><strong>出版社：</strong>{viewingBook.publisher}</p>\n                <p><strong>ISBN：</strong>{viewingBook.isbn}</p>\n                <p><strong>分类：</strong>{viewingBook.categoryName}</p>\n                <p><strong>价格：</strong>¥{viewingBook.price}</p>\n                <p><strong>库存：</strong>{viewingBook.stock}</p>\n                <p><strong>状态：</strong><Tag color={getStatusColor(viewingBook.status)}>{viewingBook.status}</Tag></p>\n              </div>\n            </div>\n            {viewingBook.description && (\n              <div>\n                <h4>图书描述</h4>\n                <p>{viewingBook.description}</p>\n              </div>\n            )}\n          </div>\n        )}\n      </Modal>\n    </Card>\n  );\n};\n\nexport default BookManagement;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,WAAW,EAEXC,OAAO,EACPC,UAAU,EACVC,IAAI,EACJC,KAAK,EACLC,GAAG,EACHC,OAAO,QACF,MAAM;AACb,SACEC,YAAY,EACZC,YAAY,EACZC,cAAc,EAEdC,WAAW,EACXC,cAAc,QACT,mBAAmB;AAE1B,SAASC,WAAW,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAM;EAAEC;AAAO,CAAC,GAAGhB,MAAM;AACzB,MAAM;EAAEiB;AAAS,CAAC,GAAGlB,KAAK;AAE1B,MAAMmB,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG7B,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC8B,UAAU,EAAEC,aAAa,CAAC,GAAG/B,QAAQ,CAAiB,EAAE,CAAC;EAChE,MAAM,CAACgC,OAAO,EAAEC,UAAU,CAAC,GAAGjC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACkC,YAAY,EAAEC,eAAe,CAAC,GAAGnC,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACwC,WAAW,EAAEC,cAAc,CAAC,GAAGzC,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAC;IAC3C4C,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGhD,QAAQ,CAAC;IAC/CiD,IAAI,EAAE,EAAE;IACRC,MAAM,EAAE,EAAE;IACVC,UAAU,EAAEC;EACd,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,CAAC,GAAG/C,IAAI,CAACgD,OAAO,CAAC,CAAC;EAE7BrD,SAAS,CAAC,MAAM;IACdsD,UAAU,CAAC,CAAC;IACZC,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACd,UAAU,CAACE,OAAO,EAAEF,UAAU,CAACG,QAAQ,CAAC,CAAC;EAE7C,MAAMU,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BtB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMwB,MAAkB,GAAG;QACzBb,OAAO,EAAEF,UAAU,CAACE,OAAO;QAC3Bc,IAAI,EAAEhB,UAAU,CAACG,QAAQ;QACzB,GAAGE;MACL,CAAC;MACD,MAAMY,QAAQ,GAAG,MAAMtC,WAAW,CAACuC,QAAQ,CAACH,MAAM,CAAC;MACnD5B,QAAQ,CAAC8B,QAAQ,CAACE,OAAO,CAAC;MAC1BlB,aAAa,CAACmB,IAAI,KAAK;QACrB,GAAGA,IAAI;QACPhB,KAAK,EAAEa,QAAQ,CAACb;MAClB,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOiB,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,UAAU,CAAC;IAC3B,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuB,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMG,QAAQ,GAAG,MAAMtC,WAAW,CAAC2C,aAAa,CAAC;QAAEpB,OAAO,EAAE,CAAC;QAAEc,IAAI,EAAE;MAAI,CAAC,CAAC;MAC3E3B,aAAa,CAAC4B,QAAQ,CAACE,OAAO,CAAC;IACjC,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,UAAU,CAAC;IAC3B;EACF,CAAC;EAED,MAAME,SAAS,GAAGA,CAAA,KAAM;IACtB1B,cAAc,CAAC,IAAI,CAAC;IACpBJ,eAAe,CAAC,IAAI,CAAC;IACrBkB,IAAI,CAACa,WAAW,CAAC,CAAC;EACpB,CAAC;EAED,MAAMC,UAAU,GAAIC,MAAY,IAAK;IACnC7B,cAAc,CAAC6B,MAAM,CAAC;IACtBjC,eAAe,CAAC,IAAI,CAAC;IACrBkB,IAAI,CAACgB,cAAc,CAACD,MAAM,CAAC;EAC7B,CAAC;EAED,MAAME,UAAU,GAAIF,MAAY,IAAK;IACnC3B,cAAc,CAAC2B,MAAM,CAAC;IACtB/B,qBAAqB,CAAC,IAAI,CAAC;EAC7B,CAAC;EAED,MAAMkC,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAMnD,WAAW,CAACoD,UAAU,CAACD,EAAE,CAAC;MAChC9D,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC;MACvBnB,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAAC,MAAM,CAAC;IACvB;EACF,CAAC;EAED,MAAMY,YAAY,GAAG,MAAOC,MAAW,IAAK;IAC1C,IAAI;MACF,IAAItC,WAAW,EAAE;QACf,MAAMjB,WAAW,CAACwD,UAAU,CAAC;UAAE,GAAGvC,WAAW;UAAE,GAAGsC;QAAO,CAAC,CAAC;QAC3DlE,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL,MAAMrD,WAAW,CAACyD,UAAU,CAACF,MAAM,CAAC;QACpClE,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC;MACzB;MACAvC,eAAe,CAAC,KAAK,CAAC;MACtBoB,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdrD,OAAO,CAACqD,KAAK,CAACzB,WAAW,GAAG,MAAM,GAAG,MAAM,CAAC;IAC9C;EACF,CAAC;EAED,MAAMyC,YAAY,GAAGA,CAAA,KAAM;IACzBpC,aAAa,CAACmB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;IAChDW,UAAU,CAAC,CAAC;EACd,CAAC;EAED,MAAMyB,WAAW,GAAGA,CAAA,KAAM;IACxBhC,eAAe,CAAC;MACdC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE,EAAE;MACVC,UAAU,EAAEC;IACd,CAAC,CAAC;IACFT,aAAa,CAACmB,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAElB,OAAO,EAAE;IAAE,CAAC,CAAC,CAAC;IAChDqC,UAAU,CAAC1B,UAAU,EAAE,GAAG,CAAC;EAC7B,CAAC;EAED,MAAM2B,cAAc,GAAIC,MAAc,IAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,KAAK;QACR,OAAO,OAAO;MAChB,KAAK,KAAK;QACR,OAAO,QAAQ;MACjB,KAAK,KAAK;QACR,OAAO,KAAK;MACd;QACE,OAAO,SAAS;IACpB;EACF,CAAC;EAED,MAAMC,OAAO,GAAG,CACd;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGC,KAAa,IACpBA,KAAK,gBACHnE,OAAA,CAACV,KAAK;MACJ2E,KAAK,EAAE,EAAG;MACVG,MAAM,EAAE,EAAG;MACXC,GAAG,EAAEF,KAAM;MACXG,QAAQ,EAAC;IAAgoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1oB,CAAC,gBAEF1E,OAAA;MAAK2E,KAAK,EAAE;QAAEV,KAAK,EAAE,EAAE;QAAEG,MAAM,EAAE,EAAE;QAAEQ,UAAU,EAAE,SAAS;QAAEC,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAC,QAAA,EAAC;IAE/H;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAGX,CAAC,EACD;IACEZ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXiB,QAAQ,EAAE;EACZ,CAAC,EACD;IACEnB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbiB,QAAQ,EAAE;EACZ,CAAC,EACD;IACEnB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAGgB,KAAa,IAAK,IAAIA,KAAK;EACtC,CAAC,EACD;IACEpB,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGN,MAAc,iBACrB5D,OAAA,CAACT,GAAG;MAAC4F,KAAK,EAAExB,cAAc,CAACC,MAAM,CAAE;MAAAoB,QAAA,EAAEpB;IAAM;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAErD,CAAC,EACD;IACEZ,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACkB,CAAM,EAAEvC,MAAY,kBAC3B7C,OAAA,CAACnB,KAAK;MAACsD,IAAI,EAAC,OAAO;MAAA6C,QAAA,gBACjBhF,OAAA,CAACR,OAAO;QAACsE,KAAK,EAAC,0BAAM;QAAAkB,QAAA,eACnBhF,OAAA,CAACpB,MAAM;UACLyG,IAAI,EAAC,MAAM;UACXlD,IAAI,EAAC,OAAO;UACZmD,IAAI,eAAEtF,OAAA,CAACJ,WAAW;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACtBa,OAAO,EAAEA,CAAA,KAAMxC,UAAU,CAACF,MAAM;QAAE;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV1E,OAAA,CAACR,OAAO;QAACsE,KAAK,EAAC,cAAI;QAAAkB,QAAA,eACjBhF,OAAA,CAACpB,MAAM;UACLyG,IAAI,EAAC,MAAM;UACXlD,IAAI,EAAC,OAAO;UACZmD,IAAI,eAAEtF,OAAA,CAACN,YAAY;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBa,OAAO,EAAEA,CAAA,KAAM3C,UAAU,CAACC,MAAM;QAAE;UAAA0B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACK,CAAC,eACV1E,OAAA,CAACZ,UAAU;QACT0E,KAAK,EAAC,oEAAa;QACnB0B,SAAS,EAAEA,CAAA,KAAMxC,YAAY,CAACH,MAAM,CAACI,EAAE,CAAE;QACzCwC,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAAV,QAAA,eAEfhF,OAAA,CAACR,OAAO;UAACsE,KAAK,EAAC,cAAI;UAAAkB,QAAA,eACjBhF,OAAA,CAACpB,MAAM;YACLyG,IAAI,EAAC,MAAM;YACXlD,IAAI,EAAC,OAAO;YACZwD,MAAM;YACNL,IAAI,eAAEtF,OAAA,CAACL,cAAc;cAAA4E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE1E,OAAA,CAACX,IAAI;IAAA2F,QAAA,gBAEHhF,OAAA;MAAK2E,KAAK,EAAE;QAAEiB,YAAY,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEjB,UAAU,EAAE,SAAS;QAAEkB,YAAY,EAAE;MAAE,CAAE;MAAAd,QAAA,eACpFhF,OAAA,CAACnB,KAAK;QAACkH,IAAI;QAAAf,QAAA,gBACThF,OAAA,CAAChB,KAAK;UACJgH,WAAW,EAAC,0BAAM;UAClBC,KAAK,EAAEzE,YAAY,CAACE,IAAK;UACzBwE,QAAQ,EAAGC,CAAC,IAAK1E,eAAe,CAACc,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEb,IAAI,EAAEyE,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAC,CAAE;UAC9EtB,KAAK,EAAE;YAAEV,KAAK,EAAE;UAAI;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACF1E,OAAA,CAAChB,KAAK;UACJgH,WAAW,EAAC,cAAI;UAChBC,KAAK,EAAEzE,YAAY,CAACG,MAAO;UAC3BuE,QAAQ,EAAGC,CAAC,IAAK1E,eAAe,CAACc,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEZ,MAAM,EAAEwE,CAAC,CAACC,MAAM,CAACH;UAAM,CAAC,CAAC,CAAE;UAChFtB,KAAK,EAAE;YAAEV,KAAK,EAAE;UAAI;QAAE;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eACF1E,OAAA,CAACf,MAAM;UACL+G,WAAW,EAAC,0BAAM;UAClBC,KAAK,EAAEzE,YAAY,CAACI,UAAW;UAC/BsE,QAAQ,EAAGD,KAAK,IAAKxE,eAAe,CAACc,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEX,UAAU,EAAEqE;UAAM,CAAC,CAAC,CAAE;UAC/EtB,KAAK,EAAE;YAAEV,KAAK,EAAE;UAAI,CAAE;UACtBoC,UAAU;UAAArB,QAAA,EAETzE,UAAU,CAAC+F,GAAG,CAACC,QAAQ,iBACtBvG,OAAA,CAACC,MAAM;YAAmBgG,KAAK,EAAEM,QAAQ,CAACtD,EAAG;YAAA+B,QAAA,EAC1CuB,QAAQ,CAAC7E;UAAI,GADH6E,QAAQ,CAACtD,EAAE;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEhB,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eACT1E,OAAA,CAACpB,MAAM;UAACyG,IAAI,EAAC,SAAS;UAACC,IAAI,eAAEtF,OAAA,CAACH,cAAc;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAACa,OAAO,EAAE/B,YAAa;UAAAwB,QAAA,EAAC;QAExE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACT1E,OAAA,CAACpB,MAAM;UAAC2G,OAAO,EAAE9B,WAAY;UAAAuB,QAAA,EAAC;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGN1E,OAAA;MAAK2E,KAAK,EAAE;QAAEiB,YAAY,EAAE;MAAG,CAAE;MAAAZ,QAAA,eAC/BhF,OAAA,CAACpB,MAAM;QACLyG,IAAI,EAAC,SAAS;QACdC,IAAI,eAAEtF,OAAA,CAACP,YAAY;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBa,OAAO,EAAE7C,SAAU;QAAAsC,QAAA,EACpB;MAED;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAGN1E,OAAA,CAACrB,KAAK;MACJkF,OAAO,EAAEA,OAAQ;MACjB2C,UAAU,EAAEnG,KAAM;MAClBI,OAAO,EAAEA,OAAQ;MACjBU,UAAU,EAAE;QACV,GAAGA,UAAU;QACbsF,eAAe,EAAE,IAAI;QACrBC,eAAe,EAAE,IAAI;QACrBC,SAAS,EAAGpF,KAAK,IAAK,KAAKA,KAAK,MAAM;QACtC2E,QAAQ,EAAEA,CAACU,IAAI,EAAEtF,QAAQ,KAAK;UAC5BF,aAAa,CAACmB,IAAI,KAAK;YACrB,GAAGA,IAAI;YACPlB,OAAO,EAAEuF,IAAI;YACbtF,QAAQ,EAAEA,QAAQ,IAAI;UACxB,CAAC,CAAC,CAAC;QACL;MACF,CAAE;MACFuF,MAAM,EAAC,IAAI;MACXC,MAAM,EAAE;QAAEC,CAAC,EAAE;MAAK;IAAE;MAAAxC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrB,CAAC,eAGF1E,OAAA,CAAClB,KAAK;MACJgF,KAAK,EAAE/C,WAAW,GAAG,MAAM,GAAG,MAAO;MACrCiG,IAAI,EAAErG,YAAa;MACnBsG,QAAQ,EAAEA,CAAA,KAAMrG,eAAe,CAAC,KAAK,CAAE;MACvCsG,IAAI,EAAEA,CAAA,KAAMpF,IAAI,CAACqF,MAAM,CAAC,CAAE;MAC1BlD,KAAK,EAAE,GAAI;MACXmD,cAAc;MAAApC,QAAA,eAEdhF,OAAA,CAACjB,IAAI;QACH+C,IAAI,EAAEA,IAAK;QACXuF,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAElE,YAAa;QAAA4B,QAAA,gBAEvBhF,OAAA,CAACjB,IAAI,CAACwI,IAAI;UACR7F,IAAI,EAAC,MAAM;UACX8F,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvI,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6F,QAAA,eAEhDhF,OAAA,CAAChB,KAAK;YAACgH,WAAW,EAAC;UAAS;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZ1E,OAAA,CAACjB,IAAI,CAACwI,IAAI;UACR7F,IAAI,EAAC,QAAQ;UACb8F,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvI,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA6F,QAAA,eAE9ChF,OAAA,CAAChB,KAAK;YAACgH,WAAW,EAAC;UAAO;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,eAEZ1E,OAAA,CAACjB,IAAI,CAACwI,IAAI;UACR7F,IAAI,EAAC,WAAW;UAChB8F,KAAK,EAAC,oBAAK;UACXC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvI,OAAO,EAAE;UAAS,CAAC,CAAE;UAAA6F,QAAA,eAE/ChF,OAAA,CAAChB,KAAK;YAACgH,WAAW,EAAC;UAAQ;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC,eAEZ1E,OAAA,CAACjB,IAAI,CAACwI,IAAI;UACR7F,IAAI,EAAC,MAAM;UACX8F,KAAK,EAAC,MAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvI,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6F,QAAA,eAEhDhF,OAAA,CAAChB,KAAK;YAACgH,WAAW,EAAC;UAAS;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZ1E,OAAA,CAACjB,IAAI,CAACwI,IAAI;UACR7F,IAAI,EAAC,YAAY;UACjB8F,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvI,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6F,QAAA,eAEhDhF,OAAA,CAACf,MAAM;YAAC+G,WAAW,EAAC,4CAAS;YAAAhB,QAAA,EAC1BzE,UAAU,CAAC+F,GAAG,CAACC,QAAQ,iBACtBvG,OAAA,CAACC,MAAM;cAAmBgG,KAAK,EAAEM,QAAQ,CAACtD,EAAG;cAAA+B,QAAA,EAC1CuB,QAAQ,CAAC7E;YAAI,GADH6E,QAAQ,CAACtD,EAAE;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEhB,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ1E,OAAA,CAACjB,IAAI,CAACwI,IAAI;UACR7F,IAAI,EAAC,OAAO;UACZ8F,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvI,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA6F,QAAA,eAE9ChF,OAAA,CAACd,WAAW;YACV8G,WAAW,EAAC,gCAAO;YACnB2B,GAAG,EAAE,CAAE;YACPC,SAAS,EAAE,CAAE;YACbjD,KAAK,EAAE;cAAEV,KAAK,EAAE;YAAO,CAAE;YACzB4D,WAAW,EAAC;UAAG;YAAAtD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1E,OAAA,CAACjB,IAAI,CAACwI,IAAI;UACR7F,IAAI,EAAC,OAAO;UACZ8F,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvI,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA6F,QAAA,eAEhDhF,OAAA,CAACd,WAAW;YACV8G,WAAW,EAAC,4CAAS;YACrB2B,GAAG,EAAE,CAAE;YACPhD,KAAK,EAAE;cAAEV,KAAK,EAAE;YAAO;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1E,OAAA,CAACjB,IAAI,CAACwI,IAAI;UACR7F,IAAI,EAAC,QAAQ;UACb8F,KAAK,EAAC,cAAI;UACVC,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEvI,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA6F,QAAA,eAE9ChF,OAAA,CAACf,MAAM;YAAC+G,WAAW,EAAC,gCAAO;YAAAhB,QAAA,gBACzBhF,OAAA,CAACC,MAAM;cAACgG,KAAK,EAAC,oBAAK;cAAAjB,QAAA,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC1E,OAAA,CAACC,MAAM;cAACgG,KAAK,EAAC,oBAAK;cAAAjB,QAAA,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC1E,OAAA,CAACC,MAAM;cAACgG,KAAK,EAAC,oBAAK;cAAAjB,QAAA,EAAC;YAAG;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ1E,OAAA,CAACjB,IAAI,CAACwI,IAAI;UACR7F,IAAI,EAAC,aAAa;UAClB8F,KAAK,EAAC,0BAAM;UAAAxC,QAAA,eAEZhF,OAAA,CAACE,QAAQ;YAAC8F,WAAW,EAAC,4CAAS;YAAC8B,IAAI,EAAE;UAAE;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClC,CAAC,eAEZ1E,OAAA,CAACjB,IAAI,CAACwI,IAAI;UACR7F,IAAI,EAAC,OAAO;UACZ8F,KAAK,EAAC,0BAAM;UAAAxC,QAAA,eAEZhF,OAAA,CAAChB,KAAK;YAACgH,WAAW,EAAC;UAAY;YAAAzB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR1E,OAAA,CAAClB,KAAK;MACJgF,KAAK,EAAC,0BAAM;MACZkD,IAAI,EAAEnG,kBAAmB;MACzBoG,QAAQ,EAAEA,CAAA,KAAMnG,qBAAqB,CAAC,KAAK,CAAE;MAC7CiH,MAAM,EAAE,IAAK;MACb9D,KAAK,EAAE,GAAI;MAAAe,QAAA,EAEV/D,WAAW,iBACVjB,OAAA;QAAAgF,QAAA,gBACEhF,OAAA;UAAK2E,KAAK,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAEmD,GAAG,EAAE,EAAE;YAAEpC,YAAY,EAAE;UAAG,CAAE;UAAAZ,QAAA,gBACzDhF,OAAA;YAAAgF,QAAA,EACG/D,WAAW,CAACkD,KAAK,gBAChBnE,OAAA,CAACV,KAAK;cACJ2E,KAAK,EAAE,GAAI;cACXG,MAAM,EAAE,GAAI;cACZC,GAAG,EAAEpD,WAAW,CAACkD,KAAM;cACvBG,QAAQ,EAAC;YAAgoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1oB,CAAC,gBAEF1E,OAAA;cAAK2E,KAAK,EAAE;gBAAEV,KAAK,EAAE,GAAG;gBAAEG,MAAM,EAAE,GAAG;gBAAEQ,UAAU,EAAE,SAAS;gBAAEC,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEC,cAAc,EAAE;cAAS,CAAE;cAAAC,QAAA,EAAC;YAEjI;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN1E,OAAA;YAAK2E,KAAK,EAAE;cAAEsD,IAAI,EAAE;YAAE,CAAE;YAAAjD,QAAA,gBACtBhF,OAAA;cAAAgF,QAAA,EAAK/D,WAAW,CAACS;YAAI;cAAA6C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAC3B1E,OAAA;cAAAgF,QAAA,gBAAGhF,OAAA;gBAAAgF,QAAA,EAAQ;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAACzD,WAAW,CAACU,MAAM;YAAA;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C1E,OAAA;cAAAgF,QAAA,gBAAGhF,OAAA;gBAAAgF,QAAA,EAAQ;cAAI;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAACzD,WAAW,CAACiH,SAAS;YAAA;cAAA3D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACnD1E,OAAA;cAAAgF,QAAA,gBAAGhF,OAAA;gBAAAgF,QAAA,EAAQ;cAAK;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAACzD,WAAW,CAACkH,IAAI;YAAA;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C1E,OAAA;cAAAgF,QAAA,gBAAGhF,OAAA;gBAAAgF,QAAA,EAAQ;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAACzD,WAAW,CAACmH,YAAY;YAAA;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrD1E,OAAA;cAAAgF,QAAA,gBAAGhF,OAAA;gBAAAgF,QAAA,EAAQ;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,QAAC,EAACzD,WAAW,CAACiE,KAAK;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/C1E,OAAA;cAAAgF,QAAA,gBAAGhF,OAAA;gBAAAgF,QAAA,EAAQ;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,EAACzD,WAAW,CAACoH,KAAK;YAAA;cAAA9D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9C1E,OAAA;cAAAgF,QAAA,gBAAGhF,OAAA;gBAAAgF,QAAA,EAAQ;cAAG;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAAA1E,OAAA,CAACT,GAAG;gBAAC4F,KAAK,EAAExB,cAAc,CAAC1C,WAAW,CAAC2C,MAAM,CAAE;gBAAAoB,QAAA,EAAE/D,WAAW,CAAC2C;cAAM;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EACLzD,WAAW,CAACqH,WAAW,iBACtBtI,OAAA;UAAAgF,QAAA,gBACEhF,OAAA;YAAAgF,QAAA,EAAI;UAAI;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACb1E,OAAA;YAAAgF,QAAA,EAAI/D,WAAW,CAACqH;UAAW;YAAA/D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEX,CAAC;AAACtE,EAAA,CAxdID,cAAwB;EAAA,QAkBbpB,IAAI,CAACgD,OAAO;AAAA;AAAAwG,EAAA,GAlBvBpI,cAAwB;AA0d9B,eAAeA,cAAc;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}