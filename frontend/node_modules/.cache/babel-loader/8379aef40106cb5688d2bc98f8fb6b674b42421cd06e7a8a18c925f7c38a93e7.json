{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Layout/MainLayout.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Layout, Menu, Breadcrumb, Avatar, Dropdown, Space, Button } from 'antd';\nimport { Routes, Route, useNavigate, useLocation } from 'react-router-dom';\nimport { MenuFoldOutlined, MenuUnfoldOutlined, UserOutlined, LogoutOutlined, DashboardOutlined, BookOutlined, TeamOutlined, ReadOutlined, PayCircleOutlined, MessageOutlined, HeartOutlined, SettingOutlined, BellOutlined } from '@ant-design/icons';\nimport { useAuth } from '../../contexts/AuthContext';\nimport Dashboard from '../../pages/Dashboard';\nimport UserManagement from '../../pages/UserManagement';\nimport BookCategoryManagement from '../../pages/BookCategoryManagement';\nimport BookManagement from '../../pages/BookManagement';\nimport BorrowManagement from '../../pages/BorrowManagement';\nimport ReturnManagement from '../../pages/ReturnManagement';\nimport FineManagement from '../../pages/FineManagement';\nimport MessageManagement from '../../pages/MessageManagement';\nimport FavoriteManagement from '../../pages/FavoriteManagement';\nimport NewsManagement from '../../pages/NewsManagement';\nimport ConfigManagement from '../../pages/ConfigManagement';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Header,\n  Sider,\n  Content\n} = Layout;\nconst MainLayout = () => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const {\n    user,\n    logout\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const menuItems = [{\n    key: '/dashboard',\n    label: '仪表板',\n    icon: /*#__PURE__*/_jsxDEV(DashboardOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 13\n    }, this),\n    path: '/dashboard'\n  }, {\n    key: 'user-management',\n    label: '用户管理',\n    icon: /*#__PURE__*/_jsxDEV(TeamOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 13\n    }, this),\n    children: [{\n      key: '/users',\n      label: '用户列表',\n      path: '/users'\n    }]\n  }, {\n    key: 'book-management',\n    label: '图书管理',\n    icon: /*#__PURE__*/_jsxDEV(BookOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 13\n    }, this),\n    children: [{\n      key: '/book-categories',\n      label: '图书分类',\n      path: '/book-categories'\n    }, {\n      key: '/books',\n      label: '图书信息',\n      path: '/books'\n    }]\n  }, {\n    key: 'borrow-management',\n    label: '借阅管理',\n    icon: /*#__PURE__*/_jsxDEV(ReadOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 13\n    }, this),\n    children: [{\n      key: '/borrows',\n      label: '图书借阅',\n      path: '/borrows'\n    }, {\n      key: '/returns',\n      label: '图书归还',\n      path: '/returns'\n    }]\n  }, {\n    key: '/fines',\n    label: '缴纳罚金',\n    icon: /*#__PURE__*/_jsxDEV(PayCircleOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 13\n    }, this),\n    path: '/fines'\n  }, {\n    key: '/messages',\n    label: '留言板管理',\n    icon: /*#__PURE__*/_jsxDEV(MessageOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 13\n    }, this),\n    path: '/messages'\n  }, {\n    key: '/favorites',\n    label: '我的收藏',\n    icon: /*#__PURE__*/_jsxDEV(HeartOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 13\n    }, this),\n    path: '/favorites'\n  }, {\n    key: 'system-management',\n    label: '系统管理',\n    icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 13\n    }, this),\n    children: [{\n      key: '/news',\n      label: '公告信息',\n      path: '/news'\n    }, {\n      key: '/config',\n      label: '轮播图管理',\n      path: '/config'\n    }]\n  }];\n  const handleMenuClick = ({\n    key\n  }) => {\n    const menuItem = findMenuItem(menuItems, key);\n    if (menuItem !== null && menuItem !== void 0 && menuItem.path) {\n      navigate(menuItem.path);\n    }\n  };\n  const findMenuItem = (items, key) => {\n    for (const item of items) {\n      if (item.key === key) return item;\n      if (item.children) {\n        const found = findMenuItem(item.children, key);\n        if (found) return found;\n      }\n    }\n    return null;\n  };\n  const getCurrentMenuItem = () => {\n    return findMenuItem(menuItems, location.pathname);\n  };\n  const getBreadcrumbItems = () => {\n    const currentItem = getCurrentMenuItem();\n    if (!currentItem) return [];\n    const items = [{\n      title: '首页'\n    }];\n\n    // 查找父级菜单\n    for (const item of menuItems) {\n      if (item.children) {\n        const found = item.children.find(child => child.key === location.pathname);\n        if (found) {\n          items.push({\n            title: item.label\n          });\n          items.push({\n            title: found.label\n          });\n          return items;\n        }\n      } else if (item.key === location.pathname) {\n        items.push({\n          title: item.label\n        });\n        return items;\n      }\n    }\n    return items;\n  };\n  const userMenuItems = [{\n    key: 'profile',\n    label: '个人信息',\n    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 13\n    }, this)\n  }, {\n    type: 'divider'\n  }, {\n    key: 'logout',\n    label: '退出登录',\n    icon: /*#__PURE__*/_jsxDEV(LogoutOutlined, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 13\n    }, this),\n    onClick: logout\n  }];\n  return /*#__PURE__*/_jsxDEV(Layout, {\n    style: {\n      minHeight: '100vh'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Sider, {\n      trigger: null,\n      collapsible: true,\n      collapsed: collapsed,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"logo\",\n        children: collapsed ? '图书馆' : '图书馆管理系统'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Menu, {\n        theme: \"dark\",\n        mode: \"inline\",\n        selectedKeys: [location.pathname],\n        items: menuItems,\n        onClick: handleMenuClick\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Layout, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        style: {\n          padding: 0,\n          background: '#fff'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"header-content\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"text\",\n            icon: collapsed ? /*#__PURE__*/_jsxDEV(MenuUnfoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 33\n            }, this) : /*#__PURE__*/_jsxDEV(MenuFoldOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 58\n            }, this),\n            onClick: () => setCollapsed(!collapsed),\n            style: {\n              fontSize: '16px',\n              width: 64,\n              height: 64\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"user-info\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(BellOutlined, {\n                style: {\n                  fontSize: '16px'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Dropdown, {\n                menu: {\n                  items: userMenuItems\n                },\n                placement: \"bottomRight\",\n                children: /*#__PURE__*/_jsxDEV(Space, {\n                  style: {\n                    cursor: 'pointer'\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                    icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 222,\n                      columnNumber: 35\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 222,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: (user === null || user === void 0 ? void 0 : user.name) || (user === null || user === void 0 ? void 0 : user.username)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 223,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Content, {\n        style: {\n          margin: '0 16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"breadcrumb-container\",\n          children: /*#__PURE__*/_jsxDEV(Breadcrumb, {\n            items: getBreadcrumbItems()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 231,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-wrapper\",\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/dashboard\",\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 236,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/users\",\n              element: /*#__PURE__*/_jsxDEV(UserManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 237,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/book-categories\",\n              element: /*#__PURE__*/_jsxDEV(BookCategoryManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 55\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/books\",\n              element: /*#__PURE__*/_jsxDEV(BookManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 239,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/borrows\",\n              element: /*#__PURE__*/_jsxDEV(BorrowManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/returns\",\n              element: /*#__PURE__*/_jsxDEV(ReturnManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 47\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 241,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/fines\",\n              element: /*#__PURE__*/_jsxDEV(FineManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 242,\n                columnNumber: 45\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/messages\",\n              element: /*#__PURE__*/_jsxDEV(MessageManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/favorites\",\n              element: /*#__PURE__*/_jsxDEV(FavoriteManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 49\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/news\",\n              element: /*#__PURE__*/_jsxDEV(NewsManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 44\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 245,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/config\",\n              element: /*#__PURE__*/_jsxDEV(ConfigManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 235,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 208,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 195,\n    columnNumber: 5\n  }, this);\n};\n_s(MainLayout, \"M8LT2MCijPr+g1hHIKxcQK5AxQc=\", false, function () {\n  return [useAuth, useNavigate, useLocation];\n});\n_c = MainLayout;\nexport default MainLayout;\nvar _c;\n$RefreshReg$(_c, \"MainLayout\");", "map": {"version": 3, "names": ["React", "useState", "Layout", "<PERSON><PERSON>", "Breadcrumb", "Avatar", "Dropdown", "Space", "<PERSON><PERSON>", "Routes", "Route", "useNavigate", "useLocation", "MenuFoldOutlined", "MenuUnfoldOutlined", "UserOutlined", "LogoutOutlined", "DashboardOutlined", "BookOutlined", "TeamOutlined", "ReadOutlined", "PayCircleOutlined", "MessageOutlined", "HeartOutlined", "SettingOutlined", "BellOutlined", "useAuth", "Dashboard", "UserManagement", "BookCategoryManagement", "BookManagement", "BorrowManagement", "ReturnManagement", "FineManagement", "MessageManagement", "FavoriteManagement", "NewsManagement", "ConfigManagement", "jsxDEV", "_jsxDEV", "Header", "<PERSON><PERSON>", "Content", "MainLayout", "_s", "collapsed", "setCollapsed", "user", "logout", "navigate", "location", "menuItems", "key", "label", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "path", "children", "handleMenuClick", "menuItem", "findMenuItem", "items", "item", "found", "getCurrentMenuItem", "pathname", "getBreadcrumbItems", "currentItem", "title", "find", "child", "push", "userMenuItems", "type", "onClick", "style", "minHeight", "trigger", "collapsible", "className", "theme", "mode", "<PERSON><PERSON><PERSON><PERSON>", "padding", "background", "fontSize", "width", "height", "menu", "placement", "cursor", "name", "username", "margin", "element", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Layout/MainLayout.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Layout, <PERSON>u, <PERSON><PERSON>crumb, Avatar, Dropdown, Space, Button } from 'antd';\nimport { Routes, Route, useNavigate, useLocation } from 'react-router-dom';\nimport {\n  MenuFoldOutlined,\n  MenuUnfoldOutlined,\n  UserOutlined,\n  LogoutOutlined,\n  DashboardOutlined,\n  BookOutlined,\n  TeamOutlined,\n  ReadOutlined,\n  PayCircleOutlined,\n  MessageOutlined,\n  HeartOutlined,\n  SettingOutlined,\n  BellOutlined,\n} from '@ant-design/icons';\nimport { useAuth } from '../../contexts/AuthContext';\nimport { MenuItem } from '../../types';\nimport Dashboard from '../../pages/Dashboard';\nimport UserManagement from '../../pages/UserManagement';\nimport BookCategoryManagement from '../../pages/BookCategoryManagement';\nimport BookManagement from '../../pages/BookManagement';\nimport BorrowManagement from '../../pages/BorrowManagement';\nimport ReturnManagement from '../../pages/ReturnManagement';\nimport FineManagement from '../../pages/FineManagement';\nimport MessageManagement from '../../pages/MessageManagement';\nimport FavoriteManagement from '../../pages/FavoriteManagement';\nimport NewsManagement from '../../pages/NewsManagement';\nimport ConfigManagement from '../../pages/ConfigManagement';\n\nconst { Header, Sider, Content } = Layout;\n\nconst MainLayout: React.FC = () => {\n  const [collapsed, setCollapsed] = useState(false);\n  const { user, logout } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  const menuItems: MenuItem[] = [\n    {\n      key: '/dashboard',\n      label: '仪表板',\n      icon: <DashboardOutlined />,\n      path: '/dashboard',\n    },\n    {\n      key: 'user-management',\n      label: '用户管理',\n      icon: <TeamOutlined />,\n      children: [\n        {\n          key: '/users',\n          label: '用户列表',\n          path: '/users',\n        },\n      ],\n    },\n    {\n      key: 'book-management',\n      label: '图书管理',\n      icon: <BookOutlined />,\n      children: [\n        {\n          key: '/book-categories',\n          label: '图书分类',\n          path: '/book-categories',\n        },\n        {\n          key: '/books',\n          label: '图书信息',\n          path: '/books',\n        },\n      ],\n    },\n    {\n      key: 'borrow-management',\n      label: '借阅管理',\n      icon: <ReadOutlined />,\n      children: [\n        {\n          key: '/borrows',\n          label: '图书借阅',\n          path: '/borrows',\n        },\n        {\n          key: '/returns',\n          label: '图书归还',\n          path: '/returns',\n        },\n      ],\n    },\n    {\n      key: '/fines',\n      label: '缴纳罚金',\n      icon: <PayCircleOutlined />,\n      path: '/fines',\n    },\n    {\n      key: '/messages',\n      label: '留言板管理',\n      icon: <MessageOutlined />,\n      path: '/messages',\n    },\n    {\n      key: '/favorites',\n      label: '我的收藏',\n      icon: <HeartOutlined />,\n      path: '/favorites',\n    },\n    {\n      key: 'system-management',\n      label: '系统管理',\n      icon: <SettingOutlined />,\n      children: [\n        {\n          key: '/news',\n          label: '公告信息',\n          path: '/news',\n        },\n        {\n          key: '/config',\n          label: '轮播图管理',\n          path: '/config',\n        },\n      ],\n    },\n  ];\n\n  const handleMenuClick = ({ key }: { key: string }) => {\n    const menuItem = findMenuItem(menuItems, key);\n    if (menuItem?.path) {\n      navigate(menuItem.path);\n    }\n  };\n\n  const findMenuItem = (items: MenuItem[], key: string): MenuItem | null => {\n    for (const item of items) {\n      if (item.key === key) return item;\n      if (item.children) {\n        const found = findMenuItem(item.children, key);\n        if (found) return found;\n      }\n    }\n    return null;\n  };\n\n  const getCurrentMenuItem = (): MenuItem | null => {\n    return findMenuItem(menuItems, location.pathname);\n  };\n\n  const getBreadcrumbItems = () => {\n    const currentItem = getCurrentMenuItem();\n    if (!currentItem) return [];\n\n    const items = [{ title: '首页' }];\n    \n    // 查找父级菜单\n    for (const item of menuItems) {\n      if (item.children) {\n        const found = item.children.find(child => child.key === location.pathname);\n        if (found) {\n          items.push({ title: item.label });\n          items.push({ title: found.label });\n          return items;\n        }\n      } else if (item.key === location.pathname) {\n        items.push({ title: item.label });\n        return items;\n      }\n    }\n\n    return items;\n  };\n\n  const userMenuItems = [\n    {\n      key: 'profile',\n      label: '个人信息',\n      icon: <UserOutlined />,\n    },\n    {\n      type: 'divider' as const,\n    },\n    {\n      key: 'logout',\n      label: '退出登录',\n      icon: <LogoutOutlined />,\n      onClick: logout,\n    },\n  ];\n\n  return (\n    <Layout style={{ minHeight: '100vh' }}>\n      <Sider trigger={null} collapsible collapsed={collapsed}>\n        <div className=\"logo\">\n          {collapsed ? '图书馆' : '图书馆管理系统'}\n        </div>\n        <Menu\n          theme=\"dark\"\n          mode=\"inline\"\n          selectedKeys={[location.pathname]}\n          items={menuItems}\n          onClick={handleMenuClick}\n        />\n      </Sider>\n      <Layout>\n        <Header style={{ padding: 0, background: '#fff' }}>\n          <div className=\"header-content\">\n            <Button\n              type=\"text\"\n              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}\n              onClick={() => setCollapsed(!collapsed)}\n              style={{ fontSize: '16px', width: 64, height: 64 }}\n            />\n            <div className=\"user-info\">\n              <Space>\n                <BellOutlined style={{ fontSize: '16px' }} />\n                <Dropdown menu={{ items: userMenuItems }} placement=\"bottomRight\">\n                  <Space style={{ cursor: 'pointer' }}>\n                    <Avatar icon={<UserOutlined />} />\n                    <span>{user?.name || user?.username}</span>\n                  </Space>\n                </Dropdown>\n              </Space>\n            </div>\n          </div>\n        </Header>\n        <Content style={{ margin: '0 16px' }}>\n          <div className=\"breadcrumb-container\">\n            <Breadcrumb items={getBreadcrumbItems()} />\n          </div>\n          <div className=\"content-wrapper\">\n            <Routes>\n              <Route path=\"/dashboard\" element={<Dashboard />} />\n              <Route path=\"/users\" element={<UserManagement />} />\n              <Route path=\"/book-categories\" element={<BookCategoryManagement />} />\n              <Route path=\"/books\" element={<BookManagement />} />\n              <Route path=\"/borrows\" element={<BorrowManagement />} />\n              <Route path=\"/returns\" element={<ReturnManagement />} />\n              <Route path=\"/fines\" element={<FineManagement />} />\n              <Route path=\"/messages\" element={<MessageManagement />} />\n              <Route path=\"/favorites\" element={<FavoriteManagement />} />\n              <Route path=\"/news\" element={<NewsManagement />} />\n              <Route path=\"/config\" element={<ConfigManagement />} />\n            </Routes>\n          </div>\n        </Content>\n      </Layout>\n    </Layout>\n  );\n};\n\nexport default MainLayout;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,IAAI,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,MAAM,QAAQ,MAAM;AAChF,SAASC,MAAM,EAAEC,KAAK,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC1E,SACEC,gBAAgB,EAChBC,kBAAkB,EAClBC,YAAY,EACZC,cAAc,EACdC,iBAAiB,EACjBC,YAAY,EACZC,YAAY,EACZC,YAAY,EACZC,iBAAiB,EACjBC,eAAe,EACfC,aAAa,EACbC,eAAe,EACfC,YAAY,QACP,mBAAmB;AAC1B,SAASC,OAAO,QAAQ,4BAA4B;AAEpD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,sBAAsB,MAAM,oCAAoC;AACvE,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,gBAAgB,MAAM,8BAA8B;AAC3D,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,iBAAiB,MAAM,+BAA+B;AAC7D,OAAOC,kBAAkB,MAAM,gCAAgC;AAC/D,OAAOC,cAAc,MAAM,4BAA4B;AACvD,OAAOC,gBAAgB,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAM;EAAEC,MAAM;EAAEC,KAAK;EAAEC;AAAQ,CAAC,GAAGxC,MAAM;AAEzC,MAAMyC,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAE8C,IAAI;IAAEC;EAAO,CAAC,GAAGtB,OAAO,CAAC,CAAC;EAClC,MAAMuB,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAC9B,MAAMuC,QAAQ,GAAGtC,WAAW,CAAC,CAAC;EAE9B,MAAMuC,SAAqB,GAAG,CAC5B;IACEC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,KAAK;IACZC,IAAI,eAAEf,OAAA,CAACtB,iBAAiB;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAEf,OAAA,CAACpB,YAAY;MAAAoC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBE,QAAQ,EAAE,CACR;MACER,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE,MAAM;MACbM,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,EACD;IACEP,GAAG,EAAE,iBAAiB;IACtBC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAEf,OAAA,CAACrB,YAAY;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBE,QAAQ,EAAE,CACR;MACER,GAAG,EAAE,kBAAkB;MACvBC,KAAK,EAAE,MAAM;MACbM,IAAI,EAAE;IACR,CAAC,EACD;MACEP,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE,MAAM;MACbM,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,EACD;IACEP,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAEf,OAAA,CAACnB,YAAY;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACtBE,QAAQ,EAAE,CACR;MACER,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE,MAAM;MACbM,IAAI,EAAE;IACR,CAAC,EACD;MACEP,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE,MAAM;MACbM,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,EACD;IACEP,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAEf,OAAA,CAAClB,iBAAiB;MAAAkC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IAC3BC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,OAAO;IACdC,IAAI,eAAEf,OAAA,CAACjB,eAAe;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAEf,OAAA,CAAChB,aAAa;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACvBC,IAAI,EAAE;EACR,CAAC,EACD;IACEP,GAAG,EAAE,mBAAmB;IACxBC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAEf,OAAA,CAACf,eAAe;MAAA+B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACzBE,QAAQ,EAAE,CACR;MACER,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE,MAAM;MACbM,IAAI,EAAE;IACR,CAAC,EACD;MACEP,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACdM,IAAI,EAAE;IACR,CAAC;EAEL,CAAC,CACF;EAED,MAAME,eAAe,GAAGA,CAAC;IAAET;EAAqB,CAAC,KAAK;IACpD,MAAMU,QAAQ,GAAGC,YAAY,CAACZ,SAAS,EAAEC,GAAG,CAAC;IAC7C,IAAIU,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEH,IAAI,EAAE;MAClBV,QAAQ,CAACa,QAAQ,CAACH,IAAI,CAAC;IACzB;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAACC,KAAiB,EAAEZ,GAAW,KAAsB;IACxE,KAAK,MAAMa,IAAI,IAAID,KAAK,EAAE;MACxB,IAAIC,IAAI,CAACb,GAAG,KAAKA,GAAG,EAAE,OAAOa,IAAI;MACjC,IAAIA,IAAI,CAACL,QAAQ,EAAE;QACjB,MAAMM,KAAK,GAAGH,YAAY,CAACE,IAAI,CAACL,QAAQ,EAAER,GAAG,CAAC;QAC9C,IAAIc,KAAK,EAAE,OAAOA,KAAK;MACzB;IACF;IACA,OAAO,IAAI;EACb,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAuB;IAChD,OAAOJ,YAAY,CAACZ,SAAS,EAAED,QAAQ,CAACkB,QAAQ,CAAC;EACnD,CAAC;EAED,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,MAAMC,WAAW,GAAGH,kBAAkB,CAAC,CAAC;IACxC,IAAI,CAACG,WAAW,EAAE,OAAO,EAAE;IAE3B,MAAMN,KAAK,GAAG,CAAC;MAAEO,KAAK,EAAE;IAAK,CAAC,CAAC;;IAE/B;IACA,KAAK,MAAMN,IAAI,IAAId,SAAS,EAAE;MAC5B,IAAIc,IAAI,CAACL,QAAQ,EAAE;QACjB,MAAMM,KAAK,GAAGD,IAAI,CAACL,QAAQ,CAACY,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACrB,GAAG,KAAKF,QAAQ,CAACkB,QAAQ,CAAC;QAC1E,IAAIF,KAAK,EAAE;UACTF,KAAK,CAACU,IAAI,CAAC;YAAEH,KAAK,EAAEN,IAAI,CAACZ;UAAM,CAAC,CAAC;UACjCW,KAAK,CAACU,IAAI,CAAC;YAAEH,KAAK,EAAEL,KAAK,CAACb;UAAM,CAAC,CAAC;UAClC,OAAOW,KAAK;QACd;MACF,CAAC,MAAM,IAAIC,IAAI,CAACb,GAAG,KAAKF,QAAQ,CAACkB,QAAQ,EAAE;QACzCJ,KAAK,CAACU,IAAI,CAAC;UAAEH,KAAK,EAAEN,IAAI,CAACZ;QAAM,CAAC,CAAC;QACjC,OAAOW,KAAK;MACd;IACF;IAEA,OAAOA,KAAK;EACd,CAAC;EAED,MAAMW,aAAa,GAAG,CACpB;IACEvB,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAEf,OAAA,CAACxB,YAAY;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE;EACvB,CAAC,EACD;IACEkB,IAAI,EAAE;EACR,CAAC,EACD;IACExB,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,MAAM;IACbC,IAAI,eAAEf,OAAA,CAACvB,cAAc;MAAAuC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;IACxBmB,OAAO,EAAE7B;EACX,CAAC,CACF;EAED,oBACET,OAAA,CAACrC,MAAM;IAAC4E,KAAK,EAAE;MAAEC,SAAS,EAAE;IAAQ,CAAE;IAAAnB,QAAA,gBACpCrB,OAAA,CAACE,KAAK;MAACuC,OAAO,EAAE,IAAK;MAACC,WAAW;MAACpC,SAAS,EAAEA,SAAU;MAAAe,QAAA,gBACrDrB,OAAA;QAAK2C,SAAS,EAAC,MAAM;QAAAtB,QAAA,EAClBf,SAAS,GAAG,KAAK,GAAG;MAAS;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3B,CAAC,eACNnB,OAAA,CAACpC,IAAI;QACHgF,KAAK,EAAC,MAAM;QACZC,IAAI,EAAC,QAAQ;QACbC,YAAY,EAAE,CAACnC,QAAQ,CAACkB,QAAQ,CAAE;QAClCJ,KAAK,EAAEb,SAAU;QACjB0B,OAAO,EAAEhB;MAAgB;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG,CAAC,eACRnB,OAAA,CAACrC,MAAM;MAAA0D,QAAA,gBACLrB,OAAA,CAACC,MAAM;QAACsC,KAAK,EAAE;UAAEQ,OAAO,EAAE,CAAC;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAA3B,QAAA,eAChDrB,OAAA;UAAK2C,SAAS,EAAC,gBAAgB;UAAAtB,QAAA,gBAC7BrB,OAAA,CAAC/B,MAAM;YACLoE,IAAI,EAAC,MAAM;YACXtB,IAAI,EAAET,SAAS,gBAAGN,OAAA,CAACzB,kBAAkB;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,gBAAGnB,OAAA,CAAC1B,gBAAgB;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAChEmB,OAAO,EAAEA,CAAA,KAAM/B,YAAY,CAAC,CAACD,SAAS,CAAE;YACxCiC,KAAK,EAAE;cAAEU,QAAQ,EAAE,MAAM;cAAEC,KAAK,EAAE,EAAE;cAAEC,MAAM,EAAE;YAAG;UAAE;YAAAnC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC,eACFnB,OAAA;YAAK2C,SAAS,EAAC,WAAW;YAAAtB,QAAA,eACxBrB,OAAA,CAAChC,KAAK;cAAAqD,QAAA,gBACJrB,OAAA,CAACd,YAAY;gBAACqD,KAAK,EAAE;kBAAEU,QAAQ,EAAE;gBAAO;cAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC7CnB,OAAA,CAACjC,QAAQ;gBAACqF,IAAI,EAAE;kBAAE3B,KAAK,EAAEW;gBAAc,CAAE;gBAACiB,SAAS,EAAC,aAAa;gBAAAhC,QAAA,eAC/DrB,OAAA,CAAChC,KAAK;kBAACuE,KAAK,EAAE;oBAAEe,MAAM,EAAE;kBAAU,CAAE;kBAAAjC,QAAA,gBAClCrB,OAAA,CAAClC,MAAM;oBAACiD,IAAI,eAAEf,OAAA,CAACxB,YAAY;sBAAAwC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAE;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAClCnB,OAAA;oBAAAqB,QAAA,EAAO,CAAAb,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,IAAI,MAAI/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgD,QAAQ;kBAAA;oBAAAxC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAO,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eACTnB,OAAA,CAACG,OAAO;QAACoC,KAAK,EAAE;UAAEkB,MAAM,EAAE;QAAS,CAAE;QAAApC,QAAA,gBACnCrB,OAAA;UAAK2C,SAAS,EAAC,sBAAsB;UAAAtB,QAAA,eACnCrB,OAAA,CAACnC,UAAU;YAAC4D,KAAK,EAAEK,kBAAkB,CAAC;UAAE;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxC,CAAC,eACNnB,OAAA;UAAK2C,SAAS,EAAC,iBAAiB;UAAAtB,QAAA,eAC9BrB,OAAA,CAAC9B,MAAM;YAAAmD,QAAA,gBACLrB,OAAA,CAAC7B,KAAK;cAACiD,IAAI,EAAC,YAAY;cAACsC,OAAO,eAAE1D,OAAA,CAACZ,SAAS;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDnB,OAAA,CAAC7B,KAAK;cAACiD,IAAI,EAAC,QAAQ;cAACsC,OAAO,eAAE1D,OAAA,CAACX,cAAc;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDnB,OAAA,CAAC7B,KAAK;cAACiD,IAAI,EAAC,kBAAkB;cAACsC,OAAO,eAAE1D,OAAA,CAACV,sBAAsB;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtEnB,OAAA,CAAC7B,KAAK;cAACiD,IAAI,EAAC,QAAQ;cAACsC,OAAO,eAAE1D,OAAA,CAACT,cAAc;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDnB,OAAA,CAAC7B,KAAK;cAACiD,IAAI,EAAC,UAAU;cAACsC,OAAO,eAAE1D,OAAA,CAACR,gBAAgB;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDnB,OAAA,CAAC7B,KAAK;cAACiD,IAAI,EAAC,UAAU;cAACsC,OAAO,eAAE1D,OAAA,CAACP,gBAAgB;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACxDnB,OAAA,CAAC7B,KAAK;cAACiD,IAAI,EAAC,QAAQ;cAACsC,OAAO,eAAE1D,OAAA,CAACN,cAAc;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACpDnB,OAAA,CAAC7B,KAAK;cAACiD,IAAI,EAAC,WAAW;cAACsC,OAAO,eAAE1D,OAAA,CAACL,iBAAiB;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DnB,OAAA,CAAC7B,KAAK;cAACiD,IAAI,EAAC,YAAY;cAACsC,OAAO,eAAE1D,OAAA,CAACJ,kBAAkB;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5DnB,OAAA,CAAC7B,KAAK;cAACiD,IAAI,EAAC,OAAO;cAACsC,OAAO,eAAE1D,OAAA,CAACH,cAAc;gBAAAmB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACnDnB,OAAA,CAAC7B,KAAK;cAACiD,IAAI,EAAC,SAAS;cAACsC,OAAO,eAAE1D,OAAA,CAACF,gBAAgB;gBAAAkB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACd,EAAA,CA1NID,UAAoB;EAAA,QAECjB,OAAO,EACff,WAAW,EACXC,WAAW;AAAA;AAAAsF,EAAA,GAJxBvD,UAAoB;AA4N1B,eAAeA,UAAU;AAAC,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}