{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ConfigManagement.tsx\";\nimport React from 'react';\nimport { Card, Typography } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst ConfigManagement = () => {\n  return /*#__PURE__*/_jsxDEV(Card, {\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u8F6E\\u64AD\\u56FE\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: \"\\u8F6E\\u64AD\\u56FE\\u7BA1\\u7406\\u529F\\u80FD\\u6B63\\u5728\\u5F00\\u53D1\\u4E2D...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = ConfigManagement;\nexport default ConfigManagement;\nvar _c;\n$RefreshReg$(_c, \"ConfigManagement\");", "map": {"version": 3, "names": ["React", "Card", "Typography", "jsxDEV", "_jsxDEV", "Title", "ConfigManagement", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ConfigManagement.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card, Typography } from 'antd';\n\nconst { Title } = Typography;\n\nconst ConfigManagement: React.FC = () => {\n  return (\n    <Card>\n      <Title level={2}>轮播图管理</Title>\n      <p>轮播图管理功能正在开发中...</p>\n    </Card>\n  );\n};\n\nexport default ConfigManagement;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,UAAU,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAM;EAAEC;AAAM,CAAC,GAAGH,UAAU;AAE5B,MAAMI,gBAA0B,GAAGA,CAAA,KAAM;EACvC,oBACEF,OAAA,CAACH,IAAI;IAAAM,QAAA,gBACHH,OAAA,CAACC,KAAK;MAACG,KAAK,EAAE,CAAE;MAAAD,QAAA,EAAC;IAAK;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAC9BR,OAAA;MAAAG,QAAA,EAAG;IAAe;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClB,CAAC;AAEX,CAAC;AAACC,EAAA,GAPIP,gBAA0B;AAShC,eAAeA,gBAAgB;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}