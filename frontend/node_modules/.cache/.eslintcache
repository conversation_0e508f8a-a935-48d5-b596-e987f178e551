[{"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/index.tsx": "1", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/App.tsx": "2", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/performance.ts": "3", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/contexts/AuthContext.tsx": "4", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Login.tsx": "5", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Auth/ProtectedRoute.tsx": "6", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Common/ErrorBoundary.tsx": "7", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Layout/MainLayout.tsx": "8", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/authService.ts": "9", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/request.ts": "10", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Dashboard.tsx": "11", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/UserManagement.tsx": "12", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookManagement.tsx": "13", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ReturnManagement.tsx": "14", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/NewsManagement.tsx": "15", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FavoriteManagement.tsx": "16", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookCategoryManagement.tsx": "17", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BorrowManagement.tsx": "18", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FineManagement.tsx": "19", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ConfigManagement.tsx": "20", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/MessageManagement.tsx": "21", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/userService.ts": "22", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/bookService.ts": "23", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/borrowService.ts": "24", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/returnService.ts": "25", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/fineService.ts": "26", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/messageService.ts": "27", "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/AdminRegister.tsx": "28"}, {"size": 657, "mtime": 1750308789615, "results": "29", "hashOfConfig": "30"}, {"size": 1128, "mtime": 1750311762111, "results": "31", "hashOfConfig": "30"}, {"size": 6105, "mtime": 1750308759431, "results": "32", "hashOfConfig": "30"}, {"size": 2134, "mtime": 1750309987604, "results": "33", "hashOfConfig": "30"}, {"size": 6457, "mtime": 1750311562341, "results": "34", "hashOfConfig": "30"}, {"size": 724, "mtime": 1750309965887, "results": "35", "hashOfConfig": "30"}, {"size": 1056, "mtime": 1750308619882, "results": "36", "hashOfConfig": "30"}, {"size": 7267, "mtime": 1750310048541, "results": "37", "hashOfConfig": "30"}, {"size": 2362, "mtime": 1750312021412, "results": "38", "hashOfConfig": "30"}, {"size": 2605, "mtime": 1750310844175, "results": "39", "hashOfConfig": "30"}, {"size": 4068, "mtime": 1750310232255, "results": "40", "hashOfConfig": "30"}, {"size": 6209, "mtime": 1750310109778, "results": "41", "hashOfConfig": "30"}, {"size": 14819, "mtime": 1750310529340, "results": "42", "hashOfConfig": "30"}, {"size": 11896, "mtime": 1750310607468, "results": "43", "hashOfConfig": "30"}, {"size": 321, "mtime": 1750304751954, "results": "44", "hashOfConfig": "30"}, {"size": 329, "mtime": 1750304741855, "results": "45", "hashOfConfig": "30"}, {"size": 5057, "mtime": 1750310251679, "results": "46", "hashOfConfig": "30"}, {"size": 13064, "mtime": 1750310546671, "results": "47", "hashOfConfig": "30"}, {"size": 12428, "mtime": 1750310566442, "results": "48", "hashOfConfig": "30"}, {"size": 319, "mtime": 1750304761971, "results": "49", "hashOfConfig": "30"}, {"size": 10253, "mtime": 1750310586330, "results": "50", "hashOfConfig": "30"}, {"size": 926, "mtime": 1750310069298, "results": "51", "hashOfConfig": "30"}, {"size": 1524, "mtime": 1750310090505, "results": "52", "hashOfConfig": "30"}, {"size": 1496, "mtime": 1750310154341, "results": "53", "hashOfConfig": "30"}, {"size": 1310, "mtime": 1750310172532, "results": "54", "hashOfConfig": "30"}, {"size": 1353, "mtime": 1750310192648, "results": "55", "hashOfConfig": "30"}, {"size": 1123, "mtime": 1750310211438, "results": "56", "hashOfConfig": "30"}, {"size": 5937, "mtime": 1750311717149, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1tyov95", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/index.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/App.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/performance.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Login.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Auth/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/components/Layout/MainLayout.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/utils/request.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/UserManagement.tsx", ["142"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookManagement.tsx", ["143"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ReturnManagement.tsx", ["144"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/NewsManagement.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FavoriteManagement.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BookCategoryManagement.tsx", ["145"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/BorrowManagement.tsx", ["146"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/FineManagement.tsx", ["147"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/ConfigManagement.tsx", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/MessageManagement.tsx", ["148"], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/userService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/bookService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/borrowService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/returnService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/fineService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/messageService.ts", [], [], "/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/AdminRegister.tsx", [], [], {"ruleId": "149", "severity": 1, "message": "150", "line": 34, "column": 6, "nodeType": "151", "endLine": 34, "endColumn": 47, "suggestions": "152"}, {"ruleId": "149", "severity": 1, "message": "153", "line": 56, "column": 6, "nodeType": "151", "endLine": 56, "endColumn": 47, "suggestions": "154"}, {"ruleId": "149", "severity": 1, "message": "155", "line": 54, "column": 6, "nodeType": "151", "endLine": 54, "endColumn": 47, "suggestions": "156"}, {"ruleId": "149", "severity": 1, "message": "157", "line": 31, "column": 6, "nodeType": "151", "endLine": 31, "endColumn": 47, "suggestions": "158"}, {"ruleId": "149", "severity": 1, "message": "159", "line": 57, "column": 6, "nodeType": "151", "endLine": 57, "endColumn": 47, "suggestions": "160"}, {"ruleId": "149", "severity": 1, "message": "161", "line": 63, "column": 6, "nodeType": "151", "endLine": 63, "endColumn": 47, "suggestions": "162"}, {"ruleId": "149", "severity": 1, "message": "163", "line": 51, "column": 6, "nodeType": "151", "endLine": 51, "endColumn": 47, "suggestions": "164"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", "ArrayExpression", ["165"], "React Hook useEffect has a missing dependency: 'fetchBooks'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["166"], "React Hook useEffect has a missing dependency: 'fetchReturns'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["167"], "React Hook useEffect has a missing dependency: 'fetchCategories'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["168"], "React Hook useEffect has a missing dependency: 'fetchBorrows'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["169"], "React Hook useEffect has a missing dependency: 'fetchFines'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["170"], "React Hook useEffect has a missing dependency: 'fetchMessages'. Either include it or remove the dependency array. Mutable values like 'pagination.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["171"], {"desc": "172", "fix": "173"}, {"desc": "174", "fix": "175"}, {"desc": "176", "fix": "177"}, {"desc": "178", "fix": "179"}, {"desc": "180", "fix": "181"}, {"desc": "182", "fix": "183"}, {"desc": "184", "fix": "185"}, "Update the dependencies array to be: [fetchUsers, pagination.pageSize]", {"range": "186", "text": "187"}, "Update the dependencies array to be: [fetchBooks, pagination.pageSize]", {"range": "188", "text": "189"}, "Update the dependencies array to be: [fetchReturns, pagination.pageSize]", {"range": "190", "text": "191"}, "Update the dependencies array to be: [fetchCategories, pagination.pageSize]", {"range": "192", "text": "193"}, "Update the dependencies array to be: [fetchBorrows, pagination.pageSize]", {"range": "194", "text": "195"}, "Update the dependencies array to be: [fetchFines, pagination.pageSize]", {"range": "196", "text": "197"}, "Update the dependencies array to be: [fetchMessages, pagination.pageSize]", {"range": "198", "text": "199"}, [830, 871], "[fetchUsers, pagination.pageSize]", [1325, 1366], "[fetchBooks, pagination.pageSize]", [1307, 1348], "[fetchReturns, pagination.pageSize]", [847, 888], "[fetchCategories, pagination.pageSize]", [1389, 1430], "[fetchBorrows, pagination.pageSize]", [1453, 1494], "[fetchFines, pagination.pageSize]", [1275, 1316], "[fetchMessages, pagination.pageSize]"]