
package com.controller;

import java.util.Arrays;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.entity.UserEntity;
import com.service.UserService;
import com.utils.MPUtil;
import com.utils.PageUtils;
import com.utils.R;

/**
 * 用户管理控制器
 */
@RequestMapping("api/users")
@RestController
public class UserController {

	@Autowired
	private UserService userService;

	/**
	 * 用户列表（分页）
	 */
	@RequestMapping("/page")
	public R page(@RequestParam Map<String, Object> params, UserEntity user) {
		EntityWrapper<UserEntity> ew = new EntityWrapper<UserEntity>();
		PageUtils page = userService.queryPage(params, MPUtil.sort(MPUtil.between(MPUtil.allLike(ew, user), params), params));
		return R.ok().put("data", page);
	}

	/**
	 * 用户列表
	 */
	@RequestMapping("/list")
	public R list(UserEntity user) {
		EntityWrapper<UserEntity> ew = new EntityWrapper<UserEntity>();
		ew.allEq(MPUtil.allEQMapPre(user, "user"));
		return R.ok().put("data", userService.selectListView(ew));
	}

	/**
	 * 用户详情
	 */
	@RequestMapping("/info/{id}")
	public R info(@PathVariable("id") String id) {
		UserEntity user = userService.selectById(id);
		return R.ok().put("data", user);
	}

	/**
	 * 获取当前用户信息
	 */
	@RequestMapping("/current")
	public R getCurrentUser(HttpServletRequest request) {
		Long id = (Long) request.getSession().getAttribute("userId");
		UserEntity user = userService.selectById(id);
		return R.ok().put("data", user);
	}
	
	/**
	 * 新增用户
	 */
	@PostMapping("/save")
	public R save(@RequestBody UserEntity user) {
		if (userService.selectOne(new EntityWrapper<UserEntity>().eq("username", user.getUsername())) != null) {
			return R.error("用户名已存在");
		}
		userService.insert(user);
		return R.ok("用户创建成功");
	}

	/**
	 * 更新用户
	 */
	@RequestMapping("/update")
	public R update(@RequestBody UserEntity user) {
		UserEntity existingUser = userService.selectOne(new EntityWrapper<UserEntity>().eq("username", user.getUsername()));
		if (existingUser != null && !existingUser.getId().equals(user.getId())) {
			return R.error("用户名已存在");
		}
		userService.updateById(user);
		return R.ok("用户更新成功");
	}

	/**
	 * 删除用户
	 */
	@RequestMapping("/delete")
	public R delete(@RequestBody Long[] ids) {
		userService.deleteBatchIds(Arrays.asList(ids));
		return R.ok("用户删除成功");
	}
}
