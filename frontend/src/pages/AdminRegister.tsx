import React, { useState } from 'react';
import { Form, Input, Button, Card, message, Alert } from 'antd';
import { UserOutlined, LockOutlined, SafetyOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import request from '../utils/request';
import { md5 } from 'js-md5';

const AdminRegister: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [accessGranted, setAccessGranted] = useState(false);
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [accessForm] = Form.useForm();

  // 管理员注册访问码（在实际项目中应该从环境变量或配置文件读取）
  const ADMIN_ACCESS_CODE = 'ADMIN_2024_SECURE';

  const handleAccessCode = (values: { accessCode: string }) => {
    if (values.accessCode === ADMIN_ACCESS_CODE) {
      setAccessGranted(true);
      message.success('访问码验证成功，可以注册管理员账户');
    } else {
      message.error('访问码错误，无法注册管理员账户');
    }
  };

  const handleAdminRegister = async (values: any) => {
    setLoading(true);
    try {
      const encryptedPassword = md5(values.password);
      
      await request.post('/api/auth/register', {
        username: values.username,
        password: encryptedPassword,
        name: values.username, // 使用用户名作为默认姓名
        role: 'admin'
      });
      
      message.success('管理员账户注册成功，请返回登录页面');
      setTimeout(() => {
        navigate('/login');
      }, 2000);
    } catch (error: any) {
      message.error(error.message || '注册失败');
    } finally {
      setLoading(false);
    }
  };

  if (!accessGranted) {
    return (
      <div className="login-container">
        <Card className="login-form" style={{ maxWidth: 400 }}>
          <div className="login-title">
            管理员注册 - 访问验证
          </div>
          
          <Alert
            message="安全提示"
            description="管理员账户注册需要特殊访问码。如果您不知道访问码，请联系系统管理员。"
            type="warning"
            showIcon
            style={{ marginBottom: 24 }}
          />

          <Form
            form={accessForm}
            onFinish={handleAccessCode}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              name="accessCode"
              rules={[{ required: true, message: '请输入访问码!' }]}
            >
              <Input
                prefix={<SafetyOutlined />}
                placeholder="请输入管理员注册访问码"
                type="password"
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                style={{ width: '100%' }}
              >
                验证访问码
              </Button>
            </Form.Item>

            <Form.Item>
              <Button
                type="default"
                onClick={() => navigate('/login')}
                style={{ width: '100%' }}
              >
                返回登录页面
              </Button>
            </Form.Item>
          </Form>
        </Card>
      </div>
    );
  }

  return (
    <div className="login-container">
      <Card className="login-form" style={{ maxWidth: 400 }}>
        <div className="login-title">
          管理员账户注册
        </div>
        
        <Alert
          message="管理员注册"
          description="您正在注册管理员账户，该账户将拥有系统的完全管理权限。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form
          form={form}
          onFinish={handleAdminRegister}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名!' },
              { min: 3, message: '用户名至少3个字符!' },
              { max: 20, message: '用户名最多20个字符!' }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="管理员用户名"
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码!' },
              { min: 6, message: '密码至少6个字符!' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="管理员密码"
            />
          </Form.Item>

          <Form.Item
            name="confirmPassword"
            dependencies={['password']}
            rules={[
              { required: true, message: '请确认密码!' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致!'));
                },
              }),
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="确认密码"
            />
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              style={{ width: '100%' }}
            >
              注册管理员账户
            </Button>
          </Form.Item>

          <Form.Item>
            <Button
              type="default"
              onClick={() => navigate('/login')}
              style={{ width: '100%' }}
            >
              返回登录页面
            </Button>
          </Form.Item>
        </Form>
      </Card>
    </div>
  );
};

export default AdminRegister;
