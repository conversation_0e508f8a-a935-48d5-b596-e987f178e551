import request from '../utils/request';
import { Book, BookCategory, PageParams, PageResponse } from '../types';

export const bookService = {
  // 图书分类相关
  async getCategories(params: PageParams): Promise<PageResponse<BookCategory>> {
    return await request.get('/tushufenlei/page', { params });
  },

  async getCategory(id: number): Promise<BookCategory> {
    return await request.get(`/tushufenlei/info/${id}`);
  },

  async createCategory(data: Omit<BookCategory, 'id'>): Promise<void> {
    await request.post('/tushufenlei/save', data);
  },

  async updateCategory(data: BookCategory): Promise<void> {
    await request.post('/tushufenlei/update', data);
  },

  async deleteCategory(id: number): Promise<void> {
    await request.post('/tushufenlei/delete', { ids: [id] });
  },

  // 图书信息相关
  async getBooks(params: PageParams): Promise<PageResponse<Book>> {
    return await request.get('/tushuxinxi/page', { params });
  },

  async getBook(id: number): Promise<Book> {
    return await request.get(`/tushuxinxi/info/${id}`);
  },

  async createBook(data: Omit<Book, 'id'>): Promise<void> {
    await request.post('/tushuxinxi/save', data);
  },

  async updateBook(data: Book): Promise<void> {
    await request.post('/tushuxinxi/update', data);
  },

  async deleteBook(id: number): Promise<void> {
    await request.post('/tushuxinxi/delete', { ids: [id] });
  },

  async deleteBooks(ids: number[]): Promise<void> {
    await request.post('/tushuxinxi/delete', { ids });
  },
};
