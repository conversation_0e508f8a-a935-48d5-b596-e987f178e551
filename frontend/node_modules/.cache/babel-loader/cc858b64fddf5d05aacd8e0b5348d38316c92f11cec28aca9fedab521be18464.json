{"ast": null, "code": "import request from '../utils/request';\nimport { md5 } from 'js-md5';\nexport const authService = {\n  // 用户登录\n  async login(params) {\n    const {\n      username,\n      password,\n      role\n    } = params;\n\n    // 密码MD5加密\n    const encryptedPassword = md5(password);\n    let response;\n    let loginUrl;\n    let loginData;\n    if (role === '管理员') {\n      // 管理员登录\n      loginUrl = '/users/login';\n      loginData = {\n        username,\n        password: encryptedPassword\n      };\n    } else {\n      // 普通用户登录\n      loginUrl = '/yonghu/login';\n      loginData = {\n        yonghuming: username,\n        mima: encryptedPassword\n      };\n    }\n    response = await request.post(loginUrl, loginData);\n\n    // 后端返回的是 { token: \"xxx\" } 格式\n    return {\n      token: response.token,\n      user: {\n        id: response.userId || 1,\n        username: username,\n        role: role || '用户',\n        name: username // 临时使用用户名作为姓名\n      }\n    };\n  },\n  // 用户注册（普通用户）\n  async register(params) {\n    const {\n      username,\n      password,\n      name,\n      phone\n    } = params;\n    const encryptedPassword = md5(password);\n\n    // 调用普通用户注册接口，使用后端字段名\n    await request.post('/yonghu/register', {\n      yonghuming: username,\n      // 用户名\n      mima: encryptedPassword,\n      // 密码\n      xingming: name,\n      // 姓名\n      shouji: phone || '' // 手机号\n    });\n  },\n  // 获取当前用户信息\n  async getCurrentUser() {\n    return await request.get('/user/current');\n  },\n  // 更新用户信息\n  async updateProfile(params) {\n    await request.put('/user/profile', params);\n  },\n  // 修改密码\n  async changePassword(params) {\n    const {\n      oldPassword,\n      newPassword\n    } = params;\n    await request.put('/user/password', {\n      oldPassword: md5(oldPassword),\n      newPassword: md5(newPassword)\n    });\n  },\n  // 退出登录\n  async logout() {\n    await request.post('/auth/logout');\n  }\n};", "map": {"version": 3, "names": ["request", "md5", "authService", "login", "params", "username", "password", "role", "encryptedPassword", "response", "loginUrl", "loginData", "yonghuming", "mima", "post", "token", "user", "id", "userId", "name", "register", "phone", "xing<PERSON>", "<PERSON><PERSON><PERSON>", "getCurrentUser", "get", "updateProfile", "put", "changePassword", "oldPassword", "newPassword", "logout"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/services/authService.ts"], "sourcesContent": ["import request from '../utils/request';\nimport { LoginParams, LoginResponse, User } from '../types';\nimport { md5 } from 'js-md5';\n\nexport const authService = {\n  // 用户登录\n  async login(params: LoginParams): Promise<LoginResponse> {\n    const { username, password, role } = params;\n\n    // 密码MD5加密\n    const encryptedPassword = md5(password);\n\n    let response;\n    let loginUrl;\n    let loginData;\n\n    if (role === '管理员') {\n      // 管理员登录\n      loginUrl = '/users/login';\n      loginData = {\n        username,\n        password: encryptedPassword,\n      };\n    } else {\n      // 普通用户登录\n      loginUrl = '/yonghu/login';\n      loginData = {\n        yonghuming: username,\n        mima: encryptedPassword,\n      };\n    }\n\n    response = await request.post(loginUrl, loginData);\n\n    // 后端返回的是 { token: \"xxx\" } 格式\n    return {\n      token: response.token,\n      user: {\n        id: response.userId || 1,\n        username: username,\n        role: role || '用户',\n        name: username, // 临时使用用户名作为姓名\n      },\n    };\n  },\n\n  // 用户注册（普通用户）\n  async register(params: {\n    username: string;\n    password: string;\n    name: string;\n    phone?: string;\n    email?: string;\n    role?: string;\n  }): Promise<void> {\n    const { username, password, name, phone } = params;\n    const encryptedPassword = md5(password);\n\n    // 调用普通用户注册接口，使用后端字段名\n    await request.post('/yonghu/register', {\n      yonghuming: username,  // 用户名\n      mima: encryptedPassword,  // 密码\n      xingming: name,  // 姓名\n      shouji: phone || '',  // 手机号\n    });\n  },\n\n  // 获取当前用户信息\n  async getCurrentUser(): Promise<User> {\n    return await request.get('/user/current');\n  },\n\n  // 更新用户信息\n  async updateProfile(params: Partial<User>): Promise<void> {\n    await request.put('/user/profile', params);\n  },\n\n  // 修改密码\n  async changePassword(params: {\n    oldPassword: string;\n    newPassword: string;\n  }): Promise<void> {\n    const { oldPassword, newPassword } = params;\n    \n    await request.put('/user/password', {\n      oldPassword: md5(oldPassword),\n      newPassword: md5(newPassword),\n    });\n  },\n\n  // 退出登录\n  async logout(): Promise<void> {\n    await request.post('/auth/logout');\n  },\n};\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,kBAAkB;AAEtC,SAASC,GAAG,QAAQ,QAAQ;AAE5B,OAAO,MAAMC,WAAW,GAAG;EACzB;EACA,MAAMC,KAAKA,CAACC,MAAmB,EAA0B;IACvD,MAAM;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC;IAAK,CAAC,GAAGH,MAAM;;IAE3C;IACA,MAAMI,iBAAiB,GAAGP,GAAG,CAACK,QAAQ,CAAC;IAEvC,IAAIG,QAAQ;IACZ,IAAIC,QAAQ;IACZ,IAAIC,SAAS;IAEb,IAAIJ,IAAI,KAAK,KAAK,EAAE;MAClB;MACAG,QAAQ,GAAG,cAAc;MACzBC,SAAS,GAAG;QACVN,QAAQ;QACRC,QAAQ,EAAEE;MACZ,CAAC;IACH,CAAC,MAAM;MACL;MACAE,QAAQ,GAAG,eAAe;MAC1BC,SAAS,GAAG;QACVC,UAAU,EAAEP,QAAQ;QACpBQ,IAAI,EAAEL;MACR,CAAC;IACH;IAEAC,QAAQ,GAAG,MAAMT,OAAO,CAACc,IAAI,CAACJ,QAAQ,EAAEC,SAAS,CAAC;;IAElD;IACA,OAAO;MACLI,KAAK,EAAEN,QAAQ,CAACM,KAAK;MACrBC,IAAI,EAAE;QACJC,EAAE,EAAER,QAAQ,CAACS,MAAM,IAAI,CAAC;QACxBb,QAAQ,EAAEA,QAAQ;QAClBE,IAAI,EAAEA,IAAI,IAAI,IAAI;QAClBY,IAAI,EAAEd,QAAQ,CAAE;MAClB;IACF,CAAC;EACH,CAAC;EAED;EACA,MAAMe,QAAQA,CAAChB,MAOd,EAAiB;IAChB,MAAM;MAAEC,QAAQ;MAAEC,QAAQ;MAAEa,IAAI;MAAEE;IAAM,CAAC,GAAGjB,MAAM;IAClD,MAAMI,iBAAiB,GAAGP,GAAG,CAACK,QAAQ,CAAC;;IAEvC;IACA,MAAMN,OAAO,CAACc,IAAI,CAAC,kBAAkB,EAAE;MACrCF,UAAU,EAAEP,QAAQ;MAAG;MACvBQ,IAAI,EAAEL,iBAAiB;MAAG;MAC1Bc,QAAQ,EAAEH,IAAI;MAAG;MACjBI,MAAM,EAAEF,KAAK,IAAI,EAAE,CAAG;IACxB,CAAC,CAAC;EACJ,CAAC;EAED;EACA,MAAMG,cAAcA,CAAA,EAAkB;IACpC,OAAO,MAAMxB,OAAO,CAACyB,GAAG,CAAC,eAAe,CAAC;EAC3C,CAAC;EAED;EACA,MAAMC,aAAaA,CAACtB,MAAqB,EAAiB;IACxD,MAAMJ,OAAO,CAAC2B,GAAG,CAAC,eAAe,EAAEvB,MAAM,CAAC;EAC5C,CAAC;EAED;EACA,MAAMwB,cAAcA,CAACxB,MAGpB,EAAiB;IAChB,MAAM;MAAEyB,WAAW;MAAEC;IAAY,CAAC,GAAG1B,MAAM;IAE3C,MAAMJ,OAAO,CAAC2B,GAAG,CAAC,gBAAgB,EAAE;MAClCE,WAAW,EAAE5B,GAAG,CAAC4B,WAAW,CAAC;MAC7BC,WAAW,EAAE7B,GAAG,CAAC6B,WAAW;IAC9B,CAAC,CAAC;EACJ,CAAC;EAED;EACA,MAAMC,MAAMA,CAAA,EAAkB;IAC5B,MAAM/B,OAAO,CAACc,IAAI,CAAC,cAAc,CAAC;EACpC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}