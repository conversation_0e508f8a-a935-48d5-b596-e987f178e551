#!/bin/bash

# 修复所有 @/ 导入为相对路径的脚本

echo "开始修复导入路径..."

# 在 src 目录下查找所有 .ts 和 .tsx 文件
find src -name "*.ts" -o -name "*.tsx" | while read file; do
    echo "处理文件: $file"
    
    # 计算相对于 src 目录的深度
    depth=$(echo "$file" | sed 's|src/||' | tr -cd '/' | wc -c)
    
    # 生成相对路径前缀
    prefix=""
    for ((i=0; i<depth; i++)); do
        prefix="../$prefix"
    done
    
    # 如果文件在 src 根目录，使用 ./ 前缀
    if [ $depth -eq 0 ]; then
        prefix="./"
    fi
    
    # 替换导入路径
    sed -i.bak \
        -e "s|from '@/types'|from '${prefix}types'|g" \
        -e "s|from '@/utils/|from '${prefix}utils/|g" \
        -e "s|from '@/services/|from '${prefix}services/|g" \
        -e "s|from '@/contexts/|from '${prefix}contexts/|g" \
        -e "s|from '@/components/|from '${prefix}components/|g" \
        -e "s|from '@/pages/|from '${prefix}pages/|g" \
        "$file"
    
    # 删除备份文件
    rm -f "$file.bak"
done

echo "导入路径修复完成！"
