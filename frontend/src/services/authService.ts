import request from '../utils/request';
import { LoginParams, LoginResponse, User } from '../types';
import * as md5 from 'js-md5';

export const authService = {
  // 用户登录
  async login(params: LoginParams): Promise<LoginResponse> {
    const { username, password, role } = params;

    // 密码MD5加密
    const encryptedPassword = md5(password);

    const response = await request.post('/auth/login', {
      username,
      password: encryptedPassword,
      role,
    });

    return {
      token: response.token,
      user: response.user,
    };
  },

  // 用户注册
  async register(params: {
    username: string;
    password: string;
    name: string;
    phone?: string;
    email?: string;
  }): Promise<void> {
    const { password, ...rest } = params;
    const encryptedPassword = md5(password);
    
    await request.post('/register', {
      ...rest,
      password: encryptedPassword,
    });
  },

  // 获取当前用户信息
  async getCurrentUser(): Promise<User> {
    return await request.get('/user/current');
  },

  // 更新用户信息
  async updateProfile(params: Partial<User>): Promise<void> {
    await request.put('/user/profile', params);
  },

  // 修改密码
  async changePassword(params: {
    oldPassword: string;
    newPassword: string;
  }): Promise<void> {
    const { oldPassword, newPassword } = params;
    
    await request.put('/user/password', {
      oldPassword: md5(oldPassword),
      newPassword: md5(newPassword),
    });
  },

  // 退出登录
  async logout(): Promise<void> {
    await request.post('/auth/logout');
  },
};
