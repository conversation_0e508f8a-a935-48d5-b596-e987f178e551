{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Login.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Form, Input, Button, Card, Select, message, Space } from 'antd';\nimport { UserOutlined, LockOutlined, UserAddOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { authService } from '../services/authService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Option\n} = Select;\nconst Login = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [registerModalVisible, setRegisterModalVisible] = useState(false);\n  const [registerLoading, setRegisterLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const [registerForm] = Form.useForm();\n  const onFinish = async values => {\n    setLoading(true);\n    try {\n      await login(values);\n      message.success('登录成功');\n      navigate('/dashboard');\n    } catch (error) {\n      message.error(error.message || '登录失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleRegister = async values => {\n    setRegisterLoading(true);\n    try {\n      await authService.register(values);\n      message.success('注册成功，请登录');\n      setRegisterModalVisible(false);\n      registerForm.resetFields();\n    } catch (error) {\n      message.error(error.message || '注册失败');\n    } finally {\n      setRegisterLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      className: \"login-form\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-title\",\n        children: \"\\u56FE\\u4E66\\u9986\\u7BA1\\u7406\\u7CFB\\u7EDF\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form, {\n        name: \"login\",\n        onFinish: onFinish,\n        autoComplete: \"off\",\n        size: \"large\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"username\",\n          rules: [{\n            required: true,\n            message: '请输入用户名!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 23\n            }, this),\n            placeholder: \"\\u7528\\u6237\\u540D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"password\",\n          rules: [{\n            required: true,\n            message: '请输入密码!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.Password, {\n            prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 73,\n              columnNumber: 23\n            }, this),\n            placeholder: \"\\u5BC6\\u7801\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"role\",\n          rules: [{\n            required: true,\n            message: '请选择角色!'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u89D2\\u8272\",\n            children: [/*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u7BA1\\u7406\\u5458\",\n              children: \"\\u7BA1\\u7406\\u5458\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Option, {\n              value: \"\\u7528\\u6237\",\n              children: \"\\u7528\\u6237\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 82,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            direction: \"vertical\",\n            style: {\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: loading,\n              style: {\n                width: '100%'\n              },\n              children: \"\\u767B\\u5F55\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"default\",\n              icon: /*#__PURE__*/_jsxDEV(UserAddOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 23\n              }, this),\n              onClick: () => setRegisterModalVisible(true),\n              style: {\n                width: '100%'\n              },\n              children: \"\\u6CE8\\u518C\\u65B0\\u7528\\u6237\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 89,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"qowO38onpEraOtyGKXQE03GFlHA=\", false, function () {\n  return [useAuth, useNavigate, Form.useForm];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Form", "Input", "<PERSON><PERSON>", "Card", "Select", "message", "Space", "UserOutlined", "LockOutlined", "UserAddOutlined", "useNavigate", "useAuth", "authService", "jsxDEV", "_jsxDEV", "Option", "<PERSON><PERSON>", "_s", "loading", "setLoading", "registerModalVisible", "setRegisterModalVisible", "registerLoading", "setRegisterLoading", "login", "navigate", "registerForm", "useForm", "onFinish", "values", "success", "error", "handleRegister", "register", "resetFields", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "autoComplete", "size", "<PERSON><PERSON>", "rules", "required", "prefix", "placeholder", "Password", "value", "direction", "style", "width", "type", "htmlType", "icon", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/pages/Login.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Form, Input, Button, Card, Select, message, Modal, Space } from 'antd';\nimport { UserOutlined, LockOutlined, UserAddOutlined } from '@ant-design/icons';\nimport { useNavigate } from 'react-router-dom';\nimport { useAuth } from '../contexts/AuthContext';\nimport { LoginParams } from '../types';\nimport { authService } from '../services/authService';\n\nconst { Option } = Select;\n\nconst Login: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [registerModalVisible, setRegisterModalVisible] = useState(false);\n  const [registerLoading, setRegisterLoading] = useState(false);\n  const { login } = useAuth();\n  const navigate = useNavigate();\n  const [registerForm] = Form.useForm();\n\n  const onFinish = async (values: LoginParams) => {\n    setLoading(true);\n    try {\n      await login(values);\n      message.success('登录成功');\n      navigate('/dashboard');\n    } catch (error: any) {\n      message.error(error.message || '登录失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleRegister = async (values: any) => {\n    setRegisterLoading(true);\n    try {\n      await authService.register(values);\n      message.success('注册成功，请登录');\n      setRegisterModalVisible(false);\n      registerForm.resetFields();\n    } catch (error: any) {\n      message.error(error.message || '注册失败');\n    } finally {\n      setRegisterLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"login-container\">\n      <Card className=\"login-form\">\n        <div className=\"login-title\">\n          图书馆管理系统\n        </div>\n        <Form\n          name=\"login\"\n          onFinish={onFinish}\n          autoComplete=\"off\"\n          size=\"large\"\n        >\n          <Form.Item\n            name=\"username\"\n            rules={[{ required: true, message: '请输入用户名!' }]}\n          >\n            <Input\n              prefix={<UserOutlined />}\n              placeholder=\"用户名\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"password\"\n            rules={[{ required: true, message: '请输入密码!' }]}\n          >\n            <Input.Password\n              prefix={<LockOutlined />}\n              placeholder=\"密码\"\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"role\"\n            rules={[{ required: true, message: '请选择角色!' }]}\n          >\n            <Select placeholder=\"请选择角色\">\n              <Option value=\"管理员\">管理员</Option>\n              <Option value=\"用户\">用户</Option>\n            </Select>\n          </Form.Item>\n\n          <Form.Item>\n            <Space direction=\"vertical\" style={{ width: '100%' }}>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={loading}\n                style={{ width: '100%' }}\n              >\n                登录\n              </Button>\n              <Button\n                type=\"default\"\n                icon={<UserAddOutlined />}\n                onClick={() => setRegisterModalVisible(true)}\n                style={{ width: '100%' }}\n              >\n                注册新用户\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Card>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAASC,KAAK,QAAQ,MAAM;AAC/E,SAASC,YAAY,EAAEC,YAAY,EAAEC,eAAe,QAAQ,mBAAmB;AAC/E,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,OAAO,QAAQ,yBAAyB;AAEjD,SAASC,WAAW,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtD,MAAM;EAAEC;AAAO,CAAC,GAAGX,MAAM;AAEzB,MAAMY,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqB,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACuB,eAAe,EAAEC,kBAAkB,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM;IAAEyB;EAAM,CAAC,GAAGb,OAAO,CAAC,CAAC;EAC3B,MAAMc,QAAQ,GAAGf,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACgB,YAAY,CAAC,GAAG1B,IAAI,CAAC2B,OAAO,CAAC,CAAC;EAErC,MAAMC,QAAQ,GAAG,MAAOC,MAAmB,IAAK;IAC9CV,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMK,KAAK,CAACK,MAAM,CAAC;MACnBxB,OAAO,CAACyB,OAAO,CAAC,MAAM,CAAC;MACvBL,QAAQ,CAAC,YAAY,CAAC;IACxB,CAAC,CAAC,OAAOM,KAAU,EAAE;MACnB1B,OAAO,CAAC0B,KAAK,CAACA,KAAK,CAAC1B,OAAO,IAAI,MAAM,CAAC;IACxC,CAAC,SAAS;MACRc,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMa,cAAc,GAAG,MAAOH,MAAW,IAAK;IAC5CN,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,MAAMX,WAAW,CAACqB,QAAQ,CAACJ,MAAM,CAAC;MAClCxB,OAAO,CAACyB,OAAO,CAAC,UAAU,CAAC;MAC3BT,uBAAuB,CAAC,KAAK,CAAC;MAC9BK,YAAY,CAACQ,WAAW,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOH,KAAU,EAAE;MACnB1B,OAAO,CAAC0B,KAAK,CAACA,KAAK,CAAC1B,OAAO,IAAI,MAAM,CAAC;IACxC,CAAC,SAAS;MACRkB,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;EAED,oBACET,OAAA;IAAKqB,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BtB,OAAA,CAACX,IAAI;MAACgC,SAAS,EAAC,YAAY;MAAAC,QAAA,gBAC1BtB,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAAC,QAAA,EAAC;MAE7B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACN1B,OAAA,CAACd,IAAI;QACHyC,IAAI,EAAC,OAAO;QACZb,QAAQ,EAAEA,QAAS;QACnBc,YAAY,EAAC,KAAK;QAClBC,IAAI,EAAC,OAAO;QAAAP,QAAA,gBAEZtB,OAAA,CAACd,IAAI,CAAC4C,IAAI;UACRH,IAAI,EAAC,UAAU;UACfI,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzC,OAAO,EAAE;UAAU,CAAC,CAAE;UAAA+B,QAAA,eAEhDtB,OAAA,CAACb,KAAK;YACJ8C,MAAM,eAAEjC,OAAA,CAACP,YAAY;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBQ,WAAW,EAAC;UAAK;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1B,OAAA,CAACd,IAAI,CAAC4C,IAAI;UACRH,IAAI,EAAC,UAAU;UACfI,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzC,OAAO,EAAE;UAAS,CAAC,CAAE;UAAA+B,QAAA,eAE/CtB,OAAA,CAACb,KAAK,CAACgD,QAAQ;YACbF,MAAM,eAAEjC,OAAA,CAACN,YAAY;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBQ,WAAW,EAAC;UAAI;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1B,OAAA,CAACd,IAAI,CAAC4C,IAAI;UACRH,IAAI,EAAC,MAAM;UACXI,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEzC,OAAO,EAAE;UAAS,CAAC,CAAE;UAAA+B,QAAA,eAE/CtB,OAAA,CAACV,MAAM;YAAC4C,WAAW,EAAC,gCAAO;YAAAZ,QAAA,gBACzBtB,OAAA,CAACC,MAAM;cAACmC,KAAK,EAAC,oBAAK;cAAAd,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAChC1B,OAAA,CAACC,MAAM;cAACmC,KAAK,EAAC,cAAI;cAAAd,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ1B,OAAA,CAACd,IAAI,CAAC4C,IAAI;UAAAR,QAAA,eACRtB,OAAA,CAACR,KAAK;YAAC6C,SAAS,EAAC,UAAU;YAACC,KAAK,EAAE;cAAEC,KAAK,EAAE;YAAO,CAAE;YAAAjB,QAAA,gBACnDtB,OAAA,CAACZ,MAAM;cACLoD,IAAI,EAAC,SAAS;cACdC,QAAQ,EAAC,QAAQ;cACjBrC,OAAO,EAAEA,OAAQ;cACjBkC,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAjB,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1B,OAAA,CAACZ,MAAM;cACLoD,IAAI,EAAC,SAAS;cACdE,IAAI,eAAE1C,OAAA,CAACL,eAAe;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC1BiB,OAAO,EAAEA,CAAA,KAAMpC,uBAAuB,CAAC,IAAI,CAAE;cAC7C+B,KAAK,EAAE;gBAAEC,KAAK,EAAE;cAAO,CAAE;cAAAjB,QAAA,EAC1B;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACvB,EAAA,CArGID,KAAe;EAAA,QAIDL,OAAO,EACRD,WAAW,EACLV,IAAI,CAAC2B,OAAO;AAAA;AAAA+B,EAAA,GAN/B1C,KAAe;AAuGrB,eAAeA,KAAK;AAAC,IAAA0C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}