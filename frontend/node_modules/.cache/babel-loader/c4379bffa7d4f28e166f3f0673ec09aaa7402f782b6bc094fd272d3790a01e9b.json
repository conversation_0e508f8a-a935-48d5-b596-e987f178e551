{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/App.tsx\";\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Layout } from 'antd';\nimport Login from './pages/Login';\nimport AdminRegister from './pages/AdminRegister';\nimport MainLayout from './components/Layout/MainLayout';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/Auth/ProtectedRoute';\nimport ErrorBoundary from './components/Common/ErrorBoundary';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n      children: /*#__PURE__*/_jsxDEV(Layout, {\n        style: {\n          minHeight: '100vh'\n        },\n        children: /*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/login\",\n            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 43\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/admin-register\",\n            element: /*#__PURE__*/_jsxDEV(AdminRegister, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 52\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/*\",\n            element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n              children: /*#__PURE__*/_jsxDEV(MainLayout, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(Navigate, {\n              to: \"/dashboard\",\n              replace: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 29,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "Layout", "<PERSON><PERSON>", "AdminRegister", "MainLayout", "<PERSON>th<PERSON><PERSON><PERSON>", "ProtectedRoute", "Error<PERSON>ou<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "children", "style", "minHeight", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "replace", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/code/LibrarySystem/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { Layout } from 'antd';\nimport Login from './pages/Login';\nimport AdminRegister from './pages/AdminRegister';\n\nimport MainLayout from './components/Layout/MainLayout';\nimport { AuthProvider } from './contexts/AuthContext';\nimport ProtectedRoute from './components/Auth/ProtectedRoute';\nimport ErrorBoundary from './components/Common/ErrorBoundary';\nimport './App.css';\n\nconst App: React.FC = () => {\n  return (\n    <ErrorBoundary>\n      <AuthProvider>\n        <Layout style={{ minHeight: '100vh' }}>\n          <Routes>\n            <Route path=\"/login\" element={<Login />} />\n            <Route path=\"/admin-register\" element={<AdminRegister />} />\n            <Route\n              path=\"/*\"\n              element={\n                <ProtectedRoute>\n                  <MainLayout />\n                </ProtectedRoute>\n              }\n            />\n            <Route path=\"/\" element={<Navigate to=\"/dashboard\" replace />} />\n          </Routes>\n        </Layout>\n      </AuthProvider>\n    </ErrorBoundary>\n  );\n};\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,MAAM,QAAQ,MAAM;AAC7B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,aAAa,MAAM,uBAAuB;AAEjD,OAAOC,UAAU,MAAM,gCAAgC;AACvD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,OAAOC,cAAc,MAAM,kCAAkC;AAC7D,OAAOC,aAAa,MAAM,mCAAmC;AAC7D,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,MAAMC,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACED,OAAA,CAACF,aAAa;IAAAI,QAAA,eACZF,OAAA,CAACJ,YAAY;MAAAM,QAAA,eACXF,OAAA,CAACR,MAAM;QAACW,KAAK,EAAE;UAAEC,SAAS,EAAE;QAAQ,CAAE;QAAAF,QAAA,eACpCF,OAAA,CAACX,MAAM;UAAAa,QAAA,gBACLF,OAAA,CAACV,KAAK;YAACe,IAAI,EAAC,QAAQ;YAACC,OAAO,eAAEN,OAAA,CAACP,KAAK;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3CV,OAAA,CAACV,KAAK;YAACe,IAAI,EAAC,iBAAiB;YAACC,OAAO,eAAEN,OAAA,CAACN,aAAa;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC5DV,OAAA,CAACV,KAAK;YACJe,IAAI,EAAC,IAAI;YACTC,OAAO,eACLN,OAAA,CAACH,cAAc;cAAAK,QAAA,eACbF,OAAA,CAACL,UAAU;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UACjB;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eACFV,OAAA,CAACV,KAAK;YAACe,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEN,OAAA,CAACT,QAAQ;cAACoB,EAAE,EAAC,YAAY;cAACC,OAAO;YAAA;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEpB,CAAC;AAACG,EAAA,GAtBIZ,GAAa;AAwBnB,eAAeA,GAAG;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}